"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.matchOrCreateCompany = matchOrCreateCompany;
const client_1 = require("@prisma/client");
const logger_1 = require("../utils/logger");
const prisma = new client_1.PrismaClient();
/**
 * Match or create a company by name
 * @param name Company name to match or create
 * @param jobData Additional job data for domain extraction
 * @param stateId Optional state ID to associate with the company
 * @returns The company ID if found or created, undefined otherwise
 */
async function matchOrCreateCompany(name, jobData, stateId) {
    if (!name || name.trim() === "" || name.toLowerCase() === "unknown") {
        logger_1.logger.debug(`🚫 Skipping empty or unknown company name: ${name}`);
        return undefined;
    }
    // Skip city names that are likely not companies
    const cityPrefixes = ["city of", "town of", "village of", "county of"];
    const cityKeywords = [
        "city",
        "town",
        "village",
        "county",
        "borough",
        "township",
        "district",
    ];
    const nameLower = name.toLowerCase();
    // Check for city prefixes
    if (cityPrefixes.some((prefix) => nameLower.startsWith(prefix))) {
        logger_1.logger.debug(`🏙️ Skipping city name as company: ${name}`);
        return undefined;
    }
    // Check for city names with city keywords
    if (cityKeywords.some((keyword) => nameLower.includes(keyword))) {
        // Additional check to avoid false positives (e.g., "City Bank")
        const commonCompanyWords = [
            "bank",
            "corp",
            "inc",
            "llc",
            "company",
            "group",
            "services",
            "international",
            "hotel",
            "market",
            "agency",
            "entertainment",
            "restaurant",
            "cafe",
            "store",
            "shop",
            "center",
            "institute",
        ];
        // Check if the name contains any common company words
        const isLikelyCompany = commonCompanyWords.some((word) => nameLower.includes(word));
        // Check if the name is a sports team or other organization with a city name
        const sportsTeamKeywords = [
            "team",
            "club",
            "united",
            "fc",
            "sc",
            "athletics",
        ];
        const isSportsTeam = sportsTeamKeywords.some((word) => nameLower.includes(word));
        // Check for specific patterns that indicate a company rather than a city
        // For example: "San Francisco 49ers", "New York Life Insurance"
        const hasNumbersOrSpecialChars = /[0-9!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(nameLower);
        // If it's likely a company, sports team, or has special patterns, don't filter it out
        if (isLikelyCompany || isSportsTeam || hasNumbersOrSpecialChars) {
            // It's likely a legitimate company with a city keyword, so don't filter it
            return null; // Continue with company matching
        }
        else {
            logger_1.logger.debug(`🏙️ Skipping potential city name as company: ${name}`);
            return undefined;
        }
    }
    // Check if the name is just a city name (e.g., "Beverly Hills")
    try {
        // Check for exact city name match
        const cityExists = await prisma.city.findFirst({
            where: {
                name: {
                    equals: name,
                    mode: "insensitive",
                },
            },
        });
        if (cityExists) {
            logger_1.logger.debug(`🏙️ Skipping exact city name match as company: ${name}`);
            return undefined;
        }
        // Check for city name with state code (e.g., "Beverly Hills, CA")
        const cityWithStateRegex = /^(.+),\s*([A-Z]{2})$/;
        const match = name.match(cityWithStateRegex);
        if (match) {
            const cityName = match[1].trim();
            const stateCode = match[2];
            const cityWithState = await prisma.city.findFirst({
                where: {
                    name: {
                        equals: cityName,
                        mode: "insensitive",
                    },
                    state: {
                        code: stateCode,
                    },
                },
            });
            if (cityWithState) {
                logger_1.logger.debug(`🏙️ Skipping city with state match as company: ${name}`);
                return undefined;
            }
        }
    }
    catch (err) {
        // If there's an error checking the city, continue with company matching
        logger_1.logger.warn(`⚠️ Error checking if name is a city: ${name}`, err);
    }
    try {
        // Try to find an existing company with the exact name
        let company = await prisma.company.findUnique({ where: { name } });
        if (company) {
            // Check if this existing company is actually a city
            const cityExists = await prisma.city.findFirst({
                where: {
                    name: {
                        equals: name,
                        mode: "insensitive",
                    },
                },
            });
            if (cityExists) {
                logger_1.logger.debug(`🏙️ Found existing company that is a city: ${name} (${company.id}) - skipping`);
                return undefined;
            }
            // Check for city with state code (e.g., "Beverly Hills, CA")
            const cityWithStateRegex = /^(.+),\s*([A-Z]{2})$/;
            const match = name.match(cityWithStateRegex);
            if (match) {
                const cityName = match[1].trim();
                const stateCode = match[2];
                const cityWithState = await prisma.city.findFirst({
                    where: {
                        name: {
                            equals: cityName,
                            mode: "insensitive",
                        },
                        state: {
                            code: stateCode,
                        },
                    },
                });
                if (cityWithState) {
                    logger_1.logger.debug(`🏙️ Found existing company that is a city with state: ${name} (${company.id}) - skipping`);
                    return undefined;
                }
            }
            // It's a legitimate company
            logger_1.logger.debug(`🏢 Found existing company: ${name} (${company.id})`);
            return company.id;
        }
        // If not found, create a new company
        // We'll extract domains in a separate batch job to avoid slowing down the scraper
        const companyData = {
            name,
            createdAt: new Date(),
        };
        // If stateId is provided, set it as the headquartersStateId
        if (stateId) {
            companyData.headquartersStateId = stateId;
            logger_1.logger.debug(`🏢 Setting headquartersStateId for new company: ${name} (${stateId})`);
        }
        try {
            company = await prisma.company.create({
                data: companyData,
            });
            logger_1.logger.debug(`🏢 Created new company: ${name} (${company.id})`);
            return company.id;
        }
        catch (error) {
            // Check if this is a unique constraint error (company with this name already exists)
            if (error.code === "P2002" && error.meta?.target?.includes("name")) {
                // Try to find the company again (it might have been created by another process)
                const existingCompany = await prisma.company.findUnique({
                    where: { name },
                });
                if (existingCompany) {
                    logger_1.logger.debug(`🏢 Found existing company after creation attempt: ${name} (${existingCompany.id})`);
                    return existingCompany.id;
                }
            }
            // Re-throw the error if it's not a unique constraint error or if we couldn't find the company
            throw error;
        }
    }
    catch (err) {
        logger_1.logger.warn(`⚠️ Failed to match or create company: ${name}`, err);
        return undefined;
    }
}
