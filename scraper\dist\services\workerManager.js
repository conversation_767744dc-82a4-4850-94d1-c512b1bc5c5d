"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.WorkerManager = void 0;
const logger_1 = require("../utils/logger");
class WorkerManager {
    workerPool;
    constructor(workerPool) {
        this.workerPool = workerPool;
    }
    /**
     * Gets a worker for a specific batch
     */
    async getWorkerForBatch(batch) {
        try {
            // Get the specific worker assigned to this batch
            const worker = await this.workerPool.getSpecificWorker(batch.workerIndex);
            if (!worker) {
                logger_1.logger.error(`❌ Failed to get a worker for batch ${batch.batchId}`);
                return null;
            }
            logger_1.logger.info(`✅ Got worker #${worker.id} for batch ${batch.batchId}`);
            // Update worker state
            worker.currentBatch = batch;
            if (batch.occupations.length > 0) {
                worker.currentJob = {
                    title: batch.occupations[0].title,
                    id: batch.occupations[0].id,
                };
            }
            return worker;
        }
        catch (error) {
            logger_1.logger.error(`Error getting worker for batch ${batch.batchId}:`, error);
            return null;
        }
    }
    /**
     * Releases a worker back to the pool
     */
    async releaseWorker(worker) {
        try {
            // Clear worker state
            worker.currentBatch = undefined;
            worker.currentJob = undefined;
            // Any additional cleanup needed
            logger_1.logger.info(`✅ Released worker #${worker.id} back to the pool`);
        }
        catch (error) {
            logger_1.logger.error(`Error releasing worker #${worker.id}:`, error);
        }
    }
    /**
     * Rotates proxy for a worker
     */
    async rotateProxyForWorker(worker) {
        logger_1.logger.info(`🔄 Rotating proxy for worker #${worker.id}...`);
        try {
            return await this.workerPool.rotateProxyForWorker(worker);
        }
        catch (error) {
            logger_1.logger.error(`Error rotating proxy for worker #${worker.id}:`, error);
            return false;
        }
    }
}
exports.WorkerManager = WorkerManager;
