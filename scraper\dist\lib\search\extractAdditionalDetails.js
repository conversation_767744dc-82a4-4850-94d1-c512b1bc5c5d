"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.extractSecurityClearance = extractSecurityClearance;
exports.extractBenefits = extractBenefits;
exports.extractTravelRequirements = extractTravelRequirements;
const logger_1 = require("../../utils/logger");
/**
 * Strip HTML tags from text
 */
function stripHtml(html) {
    return html
        .replace(/<[^>]*>/g, "") // Remove HTML tags
        .replace(/&nbsp;/g, " ") // Replace &nbsp; with space
        .replace(/&amp;/g, "&") // Replace &amp; with &
        .replace(/&lt;/g, "<") // Replace &lt; with <
        .replace(/&gt;/g, ">") // Replace &gt; with >
        .replace(/&quot;/g, '"') // Replace &quot; with "
        .replace(/&#39;/g, "'") // Replace &#39; with '
        .replace(/\s+/g, " ") // Replace multiple spaces with single space
        .trim(); // Trim whitespace
}
/**
 * Extract security clearance requirements from a job description
 */
function extractSecurityClearance(text) {
    logger_1.logger.info(`🔍 Extracting security clearance requirements from job description`);
    if (!text) {
        logger_1.logger.info(`⚠️ No description provided for security clearance extraction`);
        return undefined;
    }
    // Clean the text by removing HTML tags
    const cleanText = stripHtml(text);
    const lowerCaseDescription = cleanText.toLowerCase();
    // Common security clearance levels
    const clearanceLevels = [
        {
            name: "Top Secret/SCI",
            patterns: [
                "top secret[/\\s-]*sci",
                "ts[/\\s-]*sci",
                "top secret with sci",
                "ts with sci",
            ],
        },
        {
            name: "Top Secret",
            patterns: ["\\btop secret\\b", "\\bts\\b clearance"],
        },
        { name: "Secret", patterns: ["\\bsecret clearance\\b", "\\bsecret\\b"] },
        {
            name: "Confidential",
            patterns: ["\\bconfidential clearance\\b", "\\bconfidential\\b"],
        },
        { name: "Public Trust", patterns: ["\\bpublic trust\\b"] },
        {
            name: "Security Clearance Required",
            patterns: [
                "security clearance required",
                "must have clearance",
                "active clearance",
                "current clearance",
            ],
        },
    ];
    // Look for clearance requirements
    for (const level of clearanceLevels) {
        for (const pattern of level.patterns) {
            const regex = new RegExp(pattern, "i");
            if (regex.test(lowerCaseDescription)) {
                logger_1.logger.info(`🔑 Extracted security clearance requirement: ${level.name}`);
                return level.name;
            }
        }
    }
    logger_1.logger.info(`⚠️ No security clearance requirements found`);
    return undefined;
}
/**
 * Extract benefits from a job description
 */
function extractBenefits(text) {
    logger_1.logger.info(`🔍 Extracting benefits from job description`);
    if (!text) {
        logger_1.logger.info(`⚠️ No description provided for benefits extraction`);
        return [];
    }
    // Clean the text by removing HTML tags
    const cleanText = stripHtml(text);
    const lowerCaseDescription = cleanText.toLowerCase();
    const benefits = [];
    // Look for benefits section
    const benefitsSectionRegexes = [
        /benefits:?(.*?)(?:requirements|qualifications|about you|what you'll do|what you will do|about the role|about the job|about the position|how to apply|application process)/is,
        /perks:?(.*?)(?:requirements|qualifications|about you|what you'll do|what you will do|about the role|about the job|about the position|how to apply|application process)/is,
        /what we offer:?(.*?)(?:requirements|qualifications|about you|what you'll do|what you will do|about the role|about the job|about the position|how to apply|application process)/is,
        /compensation and benefits:?(.*?)(?:requirements|qualifications|about you|what you'll do|what you will do|about the role|about the job|about the position|how to apply|application process)/is,
    ];
    let benefitsText = "";
    // Try to find a benefits section
    for (const regex of benefitsSectionRegexes) {
        const match = text.match(regex);
        if (match && match[1]) {
            benefitsText = match[1].trim();
            logger_1.logger.info(`✅ Found benefits section with ${benefitsText.length} characters`);
            break;
        }
    }
    // If we couldn't find a dedicated section, search the whole description
    if (!benefitsText) {
        benefitsText = text;
        logger_1.logger.info(`⚠️ No specific benefits section found, using full description`);
    }
    // Common benefits to look for
    const commonBenefits = [
        {
            name: "Health Insurance",
            patterns: [
                "health insurance",
                "medical insurance",
                "medical coverage",
                "health benefits",
                "healthcare",
            ],
        },
        {
            name: "Dental Insurance",
            patterns: [
                "dental insurance",
                "dental coverage",
                "dental benefits",
                "dental",
            ],
        },
        {
            name: "Vision Insurance",
            patterns: [
                "vision insurance",
                "vision coverage",
                "vision benefits",
                "vision",
            ],
        },
        {
            name: "401(k)",
            patterns: ["401[\\(\\)k]", "retirement plan", "retirement benefits"],
        },
        {
            name: "Paid Time Off",
            patterns: [
                "paid time off",
                "pto",
                "vacation time",
                "vacation days",
                "paid vacation",
            ],
        },
        {
            name: "Remote Work",
            patterns: [
                "remote work",
                "work from home",
                "wfh",
                "telecommute",
                "flexible work location",
            ],
        },
        {
            name: "Flexible Hours",
            patterns: [
                "flexible hours",
                "flexible schedule",
                "flexible working hours",
                "work-life balance",
            ],
        },
        {
            name: "Parental Leave",
            patterns: [
                "parental leave",
                "maternity leave",
                "paternity leave",
                "family leave",
            ],
        },
        {
            name: "Professional Development",
            patterns: [
                "professional development",
                "training",
                "education reimbursement",
                "tuition reimbursement",
                "learning opportunities",
            ],
        },
        {
            name: "Stock Options",
            patterns: [
                "stock options",
                "equity",
                "esop",
                "stock purchase plan",
                "rsu",
            ],
        },
        {
            name: "Bonuses",
            patterns: ["bonus", "bonuses", "performance bonus", "annual bonus"],
        },
        {
            name: "Gym Membership",
            patterns: [
                "gym membership",
                "fitness stipend",
                "wellness program",
                "fitness benefits",
            ],
        },
        {
            name: "Commuter Benefits",
            patterns: [
                "commuter benefits",
                "transportation allowance",
                "transit benefits",
            ],
        },
        { name: "Life Insurance", patterns: ["life insurance"] },
        {
            name: "Disability Insurance",
            patterns: [
                "disability insurance",
                "short-term disability",
                "long-term disability",
            ],
        },
        {
            name: "Health Savings Account",
            patterns: [
                "health savings account",
                "hsa",
                "fsa",
                "flexible spending account",
            ],
        },
        {
            name: "Employee Assistance Program",
            patterns: ["employee assistance program", "eap", "counseling services"],
        },
        {
            name: "Relocation Assistance",
            patterns: [
                "relocation assistance",
                "relocation package",
                "moving assistance",
            ],
        },
    ];
    // Look for common benefits
    for (const benefit of commonBenefits) {
        for (const pattern of benefit.patterns) {
            const regex = new RegExp(`\\b${pattern}\\b`, "i");
            if (regex.test(lowerCaseDescription) &&
                !benefits.includes(benefit.name)) {
                benefits.push(benefit.name);
                logger_1.logger.info(`💪 Found benefit: ${benefit.name}`);
                break; // Break after finding the first pattern for this benefit
            }
        }
    }
    // Use a simpler approach to find bullet points
    const bulletPointRegex = /(?:^|\n)[\s]*[•\-\*\+][\s]+([^\n]+)/g;
    let match;
    while ((match = bulletPointRegex.exec(benefitsText)) !== null) {
        if (match[1] &&
            match[1].trim().length > 5 &&
            match[1].trim().length < 100) {
            const customBenefit = match[1].trim();
            // Check if this benefit is not similar to any we've already found
            const isDuplicate = benefits.some((b) => customBenefit.toLowerCase().includes(b.toLowerCase()) ||
                b.toLowerCase().includes(customBenefit.toLowerCase()));
            if (!isDuplicate) {
                benefits.push(customBenefit);
                logger_1.logger.info(`💪 Found custom benefit: ${customBenefit}`);
            }
        }
    }
    logger_1.logger.info(`💪 Extracted ${benefits.length} benefits`);
    return benefits;
}
/**
 * Extract travel requirements from a job description
 */
function extractTravelRequirements(text) {
    logger_1.logger.info(`🔍 Extracting travel requirements from job description`);
    if (!text) {
        logger_1.logger.info(`⚠️ No description provided for travel requirements extraction`);
        return false;
    }
    // Clean the text by removing HTML tags
    const cleanText = stripHtml(text);
    const lowerCaseDescription = cleanText.toLowerCase();
    // Common patterns for travel requirements
    const travelPatterns = [
        /(\d+)\s*%\s*(?:to\s*\d+\s*%)?\s*travel/i,
        /travel\s*(?:up to|approximately|approx|around|about)?\s*(\d+)\s*%/i,
        /travel\s*(?:is\s*)?required\s*(?:up to|approximately|approx|around|about)?\s*(\d+)\s*%/i,
        /travel\s*(?:is\s*)?(?:required|expected)\s*(\d+)\s*%/i,
        /travel\s*(?:is\s*)?(?:required|expected)\s*(?:up to|approximately|approx|around|about)?\s*(\d+)\s*%/i,
        /travel\s*(?:is\s*)?(?:required|expected)\s*(?:up to|approximately|approx|around|about)?\s*(\d+)\s*percent/i,
    ];
    // Look for percentage-based travel requirements
    for (const pattern of travelPatterns) {
        const match = lowerCaseDescription.match(pattern);
        if (match && match[1]) {
            const percentage = parseInt(match[1], 10);
            if (!isNaN(percentage) && percentage >= 0 && percentage <= 100) {
                logger_1.logger.info(`✈️ Extracted travel requirement: ${percentage}%`);
                return percentage > 0;
            }
        }
    }
    // Look for descriptive travel requirements
    const noTravelPatterns = ["no travel", "travel is not required", "0% travel"];
    for (const pattern of noTravelPatterns) {
        if (lowerCaseDescription.includes(pattern)) {
            logger_1.logger.info(`✈️ No travel required for this position`);
            return false;
        }
    }
    const travelRequiredPatterns = [
        "travel required",
        "travel is required",
        "must be willing to travel",
        "ability to travel",
        "minimal travel",
        "occasional travel",
        "infrequent travel",
        "limited travel",
        "rare travel",
        "moderate travel",
        "some travel",
        "periodic travel",
        "regular travel",
        "frequent travel",
        "extensive travel",
        "heavy travel",
        "significant travel",
        "substantial travel",
    ];
    for (const pattern of travelRequiredPatterns) {
        if (lowerCaseDescription.includes(pattern)) {
            logger_1.logger.info(`✈️ Travel is required for this position: ${pattern}`);
            return true;
        }
    }
    logger_1.logger.info(`⚠️ No travel requirements found, assuming no travel required`);
    return false;
}
