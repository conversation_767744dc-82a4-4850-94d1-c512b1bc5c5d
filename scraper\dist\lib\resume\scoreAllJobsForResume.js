"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.scoreAllJobsForResume = scoreAllJobsForResume;
const client_1 = require("@prisma/client");
const scoreJobAgainstResume_1 = require("./scoreJobAgainstResume");
const prisma = new client_1.PrismaClient();
async function scoreAllJobsForResume(resumeText) {
    const jobs = await prisma.jobListing.findMany({
        where: { isActive: true, description: { not: null } },
        take: 500, // Can tune or paginate
    });
    const scored = jobs.map((job) => ({
        ...job,
        matchScore: (0, scoreJobAgainstResume_1.scoreJobAgainstResume)(resumeText, {
            title: job.title,
            company: job.company,
            description: job.description,
        }),
    }));
    // Sort from highest to lowest match
    scored.sort((a, b) => b.matchScore - a.matchScore);
    return scored;
}
