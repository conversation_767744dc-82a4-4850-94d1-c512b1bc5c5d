"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.saveJobSearchResults = saveJobSearchResults;
const client_1 = require("@prisma/client");
const logger_1 = require("../utils/logger");
const prisma = new client_1.PrismaClient();
async function saveJobSearchResults({ scoredJobs, userId, profileId, }) {
    const entries = scoredJobs.map((job) => ({
        where: {
            userId_jobId_profileId: {
                userId,
                jobId: job.id,
                profileId,
            },
        },
        update: {
            matchScore: job.matchScore,
        },
        create: {
            userId,
            jobId: job.id,
            profileId,
            matchScore: job.matchScore,
        },
    }));
    const results = await Promise.allSettled(entries.map((entry) => prisma.jobMatchResult.upsert(entry)));
    const failed = results.filter((r) => r.status === "rejected");
    if (failed.length > 0) {
        failed.forEach((f, i) => {
            logger_1.logger.warn(`❌ Failed to upsert match ${i}: ${f.reason}`);
        });
    }
    else {
        logger_1.logger.info(`✅ Saved ${entries.length} matched jobs`);
    }
}
