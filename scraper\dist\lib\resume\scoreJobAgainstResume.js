"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.scoreJobAgainstResume = scoreJobAgainstResume;
const natural_1 = __importDefault(require("natural"));
const tokenizer = new natural_1.default.WordTokenizer();
const TfIdf = natural_1.default.TfIdf;
function scoreJobAgainstResume(resumeText, job) {
    const tfidf = new TfIdf();
    const jobText = [job.title, job.company, job.description ?? ""].join(" ");
    const jobTokens = tokenizer.tokenize(jobText.toLowerCase());
    const resumeTokens = tokenizer.tokenize(resumeText.toLowerCase());
    tfidf.addDocument(resumeTokens.join(" "));
    tfidf.addDocument(jobTokens.join(" "));
    const vector1 = tfidf.listTerms(0).reduce((acc, term) => {
        acc[term.term] = term.tfidf;
        return acc;
    }, {});
    const vector2 = tfidf.listTerms(1).reduce((acc, term) => {
        acc[term.term] = term.tfidf;
        return acc;
    }, {});
    return cosineSimilarity(vector1, vector2);
}
function cosineSimilarity(a, b) {
    const allTerms = new Set([...Object.keys(a), ...Object.keys(b)]);
    let dot = 0, magA = 0, magB = 0;
    for (const term of allTerms) {
        const valA = a[term] ?? 0;
        const valB = b[term] ?? 0;
        dot += valA * valB;
        magA += valA * valA;
        magB += valB * valB;
    }
    return magA && magB ? dot / (Math.sqrt(magA) * Math.sqrt(magB)) : 0;
}
