"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.matchJobsToAllProfiles = matchJobsToAllProfiles;
const client_1 = require("@prisma/client");
const scoreAllJobsForResume_1 = require("../lib/scoreAllJobsForResume");
const saveJobSearchResults_1 = require("../lib/saveJobSearchResults");
const logger_1 = require("../utils/logger");
const prisma = new client_1.PrismaClient();
async function matchJobsToAllProfiles() {
    logger_1.logger.info("🧠 Starting auto-matching process...");
    const profiles = await prisma.profile.findMany({
        where: { parsedResume: { not: null } },
        select: {
            id: true,
            userId: true,
            parsedResume: true,
        },
    });
    for (const profile of profiles) {
        try {
            const scored = await (0, scoreAllJobsForResume_1.scoreAllJobsForResume)(profile.parsedResume);
            const top = scored.filter((j) => j.matchScore >= 0.6).slice(0, 50);
            await (0, saveJobSearchResults_1.saveJobSearchResults)({
                scoredJobs: top.map((j) => ({ id: j.id, matchScore: j.matchScore })),
                userId: profile.userId,
                profileId: profile.id,
            });
            logger_1.logger.info(`✅ Matched ${top.length} jobs for profile ${profile.id}`);
        }
        catch (err) {
            logger_1.logger.error(`❌ Failed for profile ${profile.id}:`, err);
        }
    }
    logger_1.logger.info("🏁 Finished matching all profiles.");
}
