// Simple test to verify the environment works
import { logger } from "./utils/logger.js";

async function testEnvironment() {
  try {
    logger.info("🧪 Testing environment...");
    logger.info("✅ Logger working");
    
    // Test Prisma import
    const { PrismaClient } = await import("@prisma/client");
    logger.info("✅ Prisma import working");
    
    // Test basic imports
    const config = await import("./config.js");
    logger.info("✅ Config import working");
    
    logger.info("🎉 Environment test completed successfully!");
    
  } catch (error) {
    logger.error("❌ Environment test failed:", error);
    process.exit(1);
  }
}

testEnvironment();
