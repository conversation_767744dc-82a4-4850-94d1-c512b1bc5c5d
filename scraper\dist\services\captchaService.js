"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CaptchaService = void 0;
const logger_js_1 = require("../utils/logger.js");
const improvedCaptchaSolver_js_1 = require("../scripts/improvedCaptchaSolver.js");
const captchaDetector_js_1 = require("../utils/captchaDetector.js");
class CaptchaService {
    /**
     * Checks for CAPTCHA and handles it if detected
     */
    async checkAndHandleCaptcha(page, workerInfo) {
        try {
            // Check if CAPTCHA is present
            const { detected, reason } = await (0, captchaDetector_js_1.detectCaptcha)(page);
            if (!detected) {
                return {
                    captchaDetected: false,
                    handled: false,
                    backoffTime: 0,
                };
            }
            logger_js_1.logger.warn(`⚠️ CAPTCHA detected for worker #${workerInfo.id}. Reason: ${reason}`);
            // Try to solve the CAPTCHA
            const solved = await (0, improvedCaptchaSolver_js_1.handleCaptchaIfPresent)(page);
            if (solved) {
                logger_js_1.logger.info(`✅ Successfully solved CAPTCHA for worker #${workerInfo.id}`);
                return {
                    captchaDetected: true,
                    handled: true,
                    backoffTime: 5000, // Short delay after successful solving
                };
            }
            // Calculate backoff time based on captcha count
            const baseDelay = 60 * 1000; // 1 minute
            const maxDelay = 15 * 60 * 1000; // 15 minutes
            const backoffTime = Math.min(baseDelay * Math.pow(2, workerInfo.captchaCount), maxDelay);
            logger_js_1.logger.warn(`⚠️ Failed to solve CAPTCHA for worker #${workerInfo.id}, backing off for ${Math.round(backoffTime / 1000)} seconds`);
            return {
                captchaDetected: true,
                handled: false,
                backoffTime,
                reason,
            };
        }
        catch (error) {
            logger_js_1.logger.error(`Error in checkAndHandleCaptcha:`, error);
            return {
                captchaDetected: true, // Assume CAPTCHA to be safe
                handled: false,
                backoffTime: 60000, // 1 minute default backoff
                reason: `Error checking CAPTCHA: ${error}`,
            };
        }
    }
}
exports.CaptchaService = CaptchaService;
