"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.fetchAlternateTitles = fetchAlternateTitles;
const client_1 = require("@prisma/client");
const logger_1 = require("../../utils/logger");
const path_1 = __importDefault(require("path"));
const xlsx_1 = require("xlsx");
// Set your Excel file location (make sure it’s in your project's data folder)
const DATA_PATH = path_1.default.resolve("data/Alternate Titles.xlsx");
const prisma = new client_1.PrismaClient();
// Helper to treat blank or 'n/a' values as missing.
const isNA = (v) => !v || v.trim() === "" || v.trim().toLowerCase() === "n/a";
async function fetchAlternateTitles() {
    logger_1.logger.info("📥 Reading O*NET alternate‑titles Excel file…");
    const seen = new Set();
    let count = 0;
    try {
        // Read the workbook from disk.
        const workbook = (0, xlsx_1.read)(await Promise.resolve().then(() => __importStar(require("fs"))).then((fs) => fs.readFileSync(DATA_PATH)));
        const sheetName = workbook.SheetNames[0];
        const sheet = workbook.Sheets[sheetName];
        // Convert the sheet to JSON. Each row is now an object keyed by the header row.
        const rows = xlsx_1.utils.sheet_to_json(sheet, {
            defval: "",
        });
        // Process each row
        for (const row of rows) {
            // Expected headers (exact names as on the Excel file):
            // "O*NET-SOC Code", "Title", "Alternate Title", "Short Title", "Source(s)"
            const socCode = row["O*NET-SOC Code"]?.toString().trim();
            const category = row["Title"]?.toString().trim(); // for our 'category'
            const altTitle = row["Alternate Title"]?.toString().trim(); // main title field
            const rawShort = row["Short Title"]?.toString().trim();
            const shortTitle = isNA(rawShort) ? undefined : rawShort;
            const rawSource = row["Source(s)"]?.toString().trim();
            const source = isNA(rawSource) ? null : rawSource;
            if (!socCode || isNA(altTitle))
                continue; // Skip invalid rows
            const key = `${socCode}::${altTitle}`;
            if (seen.has(key))
                continue;
            // Skip if the record already exists in the database.
            const exists = await prisma.occupations.findFirst({
                where: { socCode, title: altTitle },
                select: { id: true },
            });
            if (exists)
                continue;
            await prisma.occupations.create({
                data: {
                    socCode,
                    title: altTitle, // the alternate title from Excel
                    shortTitle: shortTitle || null,
                    category, // category is taken from the "Title" column
                    source,
                },
            });
            seen.add(key);
            count++;
            logger_1.logger.debug(`📌 Inserted occupation: ${socCode} | category="${category}" | title="${altTitle}"` +
                (shortTitle ? ` | shortTitle="${shortTitle}"` : ""));
        }
    }
    catch (err) {
        logger_1.logger.error(`❌ Error processing Excel file: ${err}`);
        throw err;
    }
    logger_1.logger.info(`✅ Stored ${count} new alternate titles in Occupations schema`);
}
