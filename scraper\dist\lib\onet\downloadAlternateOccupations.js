"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.downloadOnetAlternateOccupations = downloadOnetAlternateOccupations;
const playwright_1 = require("playwright");
const promises_1 = __importDefault(require("fs/promises"));
const path_1 = __importDefault(require("path"));
const logger_1 = require("../../utils/logger");
const axios_1 = __importDefault(require("axios"));
const FILE_URL = "https://www.onetcenter.org/dl_files/database/db_29_2_excel/Alternate%20Titles.xlsx";
// Microsoft Office Online Viewer URL (as a fallback)
const OFFICE_VIEWER_URL = "https://view.officeapps.live.com/op/view.aspx?src=https%3A%2F%2Fwww.onetcenter.org%2Fdl_files%2Fdatabase%2Fdb_29_2_excel%2FAlternate%2520Titles.xlsx";
const OUTPUT_DIR = path_1.default.resolve("data");
// Use a friendly filename without URL encoding.
const OUTPUT_FILE = path_1.default.join(OUTPUT_DIR, "Alternate Titles.xlsx");
async function downloadOnetAlternateOccupations() {
    logger_1.logger.info("🔄 Starting download of O*NET Alternate Occupations data...");
    try {
        // First try direct download with axios
        try {
            logger_1.logger.info(`📥 Attempting direct download from: ${FILE_URL}`);
            const response = await axios_1.default.get(FILE_URL, {
                responseType: "arraybuffer",
                timeout: 30000,
                headers: {
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
                },
            });
            if (response.status === 200 && response.data) {
                await promises_1.default.mkdir(OUTPUT_DIR, { recursive: true });
                await promises_1.default.writeFile(OUTPUT_FILE, Buffer.from(response.data));
                logger_1.logger.info(`✅ File successfully downloaded and saved to: ${OUTPUT_FILE}`);
                return;
            }
        }
        catch (axiosErr) {
            logger_1.logger.warn(`⚠️ Direct download failed: ${axiosErr.message || "Unknown error"}. Trying browser method...`);
        }
        // If direct download fails, try with browser automation
        logger_1.logger.info("🌐 Launching browser to download file...");
        // Force headless mode in production
        const isProduction = process.env.NODE_ENV === "production";
        const browser = await playwright_1.chromium.launch({
            headless: isProduction, // Use headless in production, headful in development for debugging
            args: ["--no-sandbox"], // Required for Docker environments
        });
        try {
            const context = await browser.newContext();
            const page = await context.newPage();
            // Try the Office Online Viewer as a fallback
            logger_1.logger.info(`📄 Navigating to Office Online Viewer: ${OFFICE_VIEWER_URL}`);
            await page.goto(OFFICE_VIEWER_URL, {
                timeout: 60000,
                waitUntil: "domcontentloaded",
            });
            // Wait for the page to load and for the download button to be available
            await page.waitForTimeout(5000);
            // Look for download button or other elements
            logger_1.logger.info("🔍 Looking for download options...");
            // Wait for user to manually download the file
            logger_1.logger.info("⏳ Please manually download the file from the browser window...");
            await page.waitForTimeout(30000); // Give user 30 seconds to download
            // Check if file exists in downloads folder
            const downloadsPath = path_1.default.join(process.env.USERPROFILE || process.env.HOME || "", "Downloads");
            const possibleFiles = [
                path_1.default.join(downloadsPath, "Alternate Titles.xlsx"),
                path_1.default.join(downloadsPath, "Alternate%20Titles.xlsx"),
            ];
            let downloadedFile = null;
            for (const file of possibleFiles) {
                try {
                    await promises_1.default.access(file);
                    downloadedFile = file;
                    break;
                }
                catch { }
            }
            if (downloadedFile) {
                // Copy the file to our data directory
                const fileContent = await promises_1.default.readFile(downloadedFile);
                await promises_1.default.mkdir(OUTPUT_DIR, { recursive: true });
                await promises_1.default.writeFile(OUTPUT_FILE, fileContent);
                logger_1.logger.info(`✅ File copied from downloads to: ${OUTPUT_FILE}`);
            }
            else {
                throw new Error("Could not find downloaded file in Downloads folder");
            }
        }
        finally {
            await browser.close();
        }
    }
    catch (err) {
        logger_1.logger.error("❌ Failed to download file:", err);
        throw err; // Re-throw to signal failure to the calling code
    }
}
