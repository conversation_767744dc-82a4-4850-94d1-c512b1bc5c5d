// cron/jobs/parallelJobScraper.ts
// This file implements a parallel job scraper with a batch queue system

import { logger } from "../utils/logger";
import { WorkerPool } from "../workers/workerPool";
import { processJobBatch } from "../workers/jobScraperWorker";
import { JobBatch } from "../utils/types";
import { runFastJobScraper } from "./fastJobScraper";
import {
  sendEmailNotification,
  EmailNotificationType,
} from "../utils/emailService";
import { v4 as uuidv4 } from "uuid";
import { initLocationCache } from "../lib/cleanJobData";
import { ProgressTracker } from "../services/progressTracker";
import os from "os";
import {
  ImprovedImprovedCircuitBreaker,
  CircuitState,
} from "../utils/improvedImprovedCircuitBreaker";
import { EnhancedEnhancedAdaptiveResourceManager } from "../utils/enhancedEnhancedAdaptiveResourceManager";
import { getPrismaClient } from "../utils/prismaClient";
import { config } from "../config";
import Redis from "ioredis";
// Removed withJobLock import since we're running standalone without Redis locking
import { getSharedCircuitBreaker } from "../utils/sharedCircuitBreaker";

// Initialize Prisma client variable
let prisma: any;

// Define types for better type safety
interface Occupation {
  id: string;
  title: string;
}

interface City {
  id: string;
  name: string;
  state: {
    id: string;
    code: string;
    name: string;
  };
}

// Create Redis client for locking
const redis = new Redis(config.redis.url as string);

// Get configuration values from config
const DEFAULT_MAX_WORKERS = config.jobs.jobScraper.maxConcurrentRequests;
const DEFAULT_BATCH_SIZE = config.jobs.jobScraper.batchSize;
const DEFAULT_CONCURRENCY = config.jobs.jobScraper.maxConcurrentRequests;
// Default to 5 occupations per batch if not specified in config
const MAX_OCCUPATIONS_PER_BATCH = 5;
const DEFAULT_HEADLESS = true; // Headless mode for better performance

// Force headless mode in production
const isProduction = process.env.NODE_ENV === "production";

// Get configuration from environment variables or use defaults
const MAX_WORKERS = parseInt(
  process.env.SCRAPER_MAX_WORKERS || DEFAULT_MAX_WORKERS.toString(),
  10
);
const BATCH_SIZE = parseInt(
  process.env.SCRAPER_BATCH_SIZE || DEFAULT_BATCH_SIZE.toString(),
  10
);
const CONCURRENCY = parseInt(
  process.env.SCRAPER_CONCURRENCY || DEFAULT_CONCURRENCY.toString(),
  10
);
// Use headless mode in production, otherwise use default
const HEADLESS = isProduction ? true : DEFAULT_HEADLESS;

// Validate configuration
if (MAX_WORKERS < 1) {
  logger.warn(
    `⚠️ Invalid MAX_WORKERS value: ${MAX_WORKERS}, using default: ${DEFAULT_MAX_WORKERS}`
  );
}
if (BATCH_SIZE < 1) {
  logger.warn(
    `⚠️ Invalid BATCH_SIZE value: ${BATCH_SIZE}, using default: ${DEFAULT_BATCH_SIZE}`
  );
}
if (CONCURRENCY < 1) {
  logger.warn(
    `⚠️ Invalid CONCURRENCY value: ${CONCURRENCY}, using default: ${DEFAULT_CONCURRENCY}`
  );
}

// Delay function
const delay = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

// Adaptive memory monitoring function with improved handling of other jobs
function monitorMemory(
  intervalMs = 60000,
  workerPool?: WorkerPool,
  otherJobsRunning = false,
  circuitBreaker?: ImprovedImprovedCircuitBreaker
): NodeJS.Timeout {
  // Create an adaptive resource manager with more aggressive settings when other jobs are running
  const resourceManager = new EnhancedEnhancedAdaptiveResourceManager({
    initialWorkerCount: workerPool
      ? workerPool.getCurrentMaxWorkers()
      : DEFAULT_MAX_WORKERS,
    minWorkerCount: 1,
    maxWorkerCount: Math.min(os.cpus().length, DEFAULT_MAX_WORKERS), // Ensure we don't exceed CPU count
    memoryThresholdPercent: otherJobsRunning ? 60 : 70, // More aggressive when other jobs are running
    criticalMemoryThresholdPercent: otherJobsRunning ? 75 : 80, // More aggressive when other jobs are running
    cpuThresholdPercent: otherJobsRunning ? 60 : 70, // More aggressive when other jobs are running
    criticalCpuThresholdPercent: otherJobsRunning ? 75 : 80, // More aggressive when other jobs are running
    scaleDownStep: otherJobsRunning ? 2 : 1, // Scale down faster when other jobs are running
    scaleUpStep: 1, // Scale up by 1 worker at a time
    stabilizationPeriodMs: 120000, // Increased from 60s to 120s
    consecutiveReadingsForScaleDown: otherJobsRunning ? 1 : 2, // Immediate scale down when other jobs are running
    consecutiveReadingsForScaleUp: 3, // Scale up after 3 consecutive low readings
    onWorkerCountChange: async (newCount: number, reason: string) => {
      if (workerPool) {
        workerPool.setMaxWorkers(newCount);
        logger.info(`Worker count adjusted to ${newCount} (reason: ${reason})`);

        // If we're scaling down to minimum due to critical resources, try to force GC
        if (newCount === 1 && reason.includes("critical")) {
          if (global.gc) {
            logger.info(
              `🧹 Running garbage collection after scaling to minimum workers...`
            );
            global.gc();

            // Log memory after GC
            const memoryUsagePercent =
              ((os.totalmem() - os.freemem()) / os.totalmem()) * 100;
            logger.info(
              `🧠 Memory after GC: ${memoryUsagePercent.toFixed(2)}%`
            );

            // If memory is still critical after GC, open the circuit breaker
            if (memoryUsagePercent > 80 && circuitBreaker) {
              circuitBreaker
                .isClosed()
                .then((isClosed) => {
                  if (isClosed) {
                    logger.warn(
                      `🔴 Opening circuit breaker due to critical memory usage after GC`
                    );
                    circuitBreaker.openCircuit(
                      "critical memory usage after GC"
                    );
                  }
                })
                .catch((err) => {
                  logger.error(`Error checking circuit breaker state: ${err}`);
                });
            }
          }
        }
      }
    },
  });

  // Track consecutive critical memory readings
  let consecutiveCriticalCount = 0;
  const criticalMemoryThresholdPercent = otherJobsRunning ? 75 : 80; // More aggressive when other jobs are running

  return setInterval(() => {
    // Get current memory and CPU usage before adjusting resources
    const totalMemory = os.totalmem();
    const freeMemory = os.freemem();
    const usedMemory = totalMemory - freeMemory;
    const memoryUsagePercent = (usedMemory / totalMemory) * 100;

    const cpuCount = os.cpus().length;
    const loadAvg = os.loadavg()[0]; // 1 minute load average
    const cpuUsagePercent = (loadAvg / cpuCount) * 100;

    // Log current resource usage when other jobs are running
    if (otherJobsRunning) {
      logger.info(
        `🧠 Memory usage: ${memoryUsagePercent.toFixed(2)}% (${(
          usedMemory /
          1024 /
          1024 /
          1024
        ).toFixed(2)} GB / ${(totalMemory / 1024 / 1024 / 1024).toFixed(
          2
        )} GB) | CPU load: ${loadAvg.toFixed(2)} (${cpuUsagePercent.toFixed(
          2
        )}%)`
      );
    }

    // Detect other jobs running based on resource usage patterns
    const detectedOtherJobs =
      !otherJobsRunning &&
      ((memoryUsagePercent > 75 && cpuUsagePercent < 50) || // High memory but low CPU often indicates other memory-intensive jobs
        (memoryUsagePercent > 70 &&
          resourceManager.getLastMemoryUsageDelta() > 10)); // Sudden memory spike

    if (detectedOtherJobs) {
      logger.warn(
        `⚠️ Detected other jobs running - adjusting resource usage accordingly`
      );
      otherJobsRunning = true;
    }

    // Check memory and adjust resources, passing the otherJobsRunning flag
    resourceManager.checkAndAdjustResources(otherJobsRunning);

    // Try garbage collection if memory usage is high
    if (memoryUsagePercent > (otherJobsRunning ? 75 : 80)) {
      logger.warn(
        `⚠️ High memory usage detected: ${memoryUsagePercent.toFixed(2)}%`
      );

      // Try to free up memory
      if (global.gc) {
        logger.info(`🧹 Running garbage collection...`);
        global.gc();

        // Check memory after GC
        const postGCFreeMemory = os.freemem();
        const postGCUsedMemory = totalMemory - postGCFreeMemory;
        const postGCMemoryUsagePercent = (postGCUsedMemory / totalMemory) * 100;

        logger.info(
          `🧠 Memory after GC: ${postGCMemoryUsagePercent.toFixed(
            2
          )}% (freed ${((usedMemory - postGCUsedMemory) / 1024 / 1024).toFixed(
            2
          )} MB)`
        );

        // Check for critical memory usage even after GC
        if (postGCMemoryUsagePercent > criticalMemoryThresholdPercent) {
          consecutiveCriticalCount++;
          logger.error(
            `🚨 Critical memory usage after GC: ${postGCMemoryUsagePercent.toFixed(
              2
            )}% (${consecutiveCriticalCount} consecutive)`
          );

          // If we've had multiple consecutive critical readings, take more drastic action
          if (consecutiveCriticalCount >= (otherJobsRunning ? 2 : 3)) {
            logger.error(
              `💥 Multiple consecutive critical memory readings - emergency action required`
            );

            // Open circuit breaker if available
            if (circuitBreaker) {
              circuitBreaker
                .isClosed()
                .then((isClosed) => {
                  if (isClosed) {
                    logger.warn(
                      `🔴 Opening circuit breaker due to consecutive critical memory readings`
                    );
                    circuitBreaker.openCircuit(
                      "consecutive critical memory readings"
                    );
                  }
                })
                .catch((err) => {
                  logger.error(`Error checking circuit breaker state: ${err}`);
                });
            }

            if (workerPool) {
              // Reduce to absolute minimum workers first
              workerPool.setMaxWorkers(1);
              logger.info(
                `🛑 Reduced worker pool to absolute minimum (1 worker)`
              );

              // If still critical after multiple readings, consider shutdown
              if (consecutiveCriticalCount >= (otherJobsRunning ? 3 : 4)) {
                logger.error(
                  `💥 Critical situation persists - emergency shutdown`
                );
                workerPool.shutdown().catch((err) => {
                  logger.error(`Error shutting down worker pool: ${err}`);
                });

                // Exit with specific code if situation is dire
                if (postGCMemoryUsagePercent > 85) {
                  logger.error(
                    `❌ System resources critically constrained, exiting with code 78`
                  );
                  process.exit(78); // Use a specific exit code for resource constraints
                }
              }
            }
          }
        } else {
          // Reset consecutive count if memory usage is back under control
          consecutiveCriticalCount = 0;
        }
      }
    } else {
      // Reset consecutive count if memory usage is under control
      consecutiveCriticalCount = 0;
    }
  }, intervalMs);
}

/**
 * Main function to run the job with proper cleanup
 */

/**
 * Get the current circuit breaker state from Redis
 */
async function getCircuitBreakerState(): Promise<CircuitState> {
  try {
    const state = await redis.get("circuit_breaker:state");
    return (state as CircuitState) || CircuitState.CLOSED;
  } catch (error) {
    logger.error(
      `❌ Error getting circuit breaker state: ${
        error instanceof Error ? error.message : String(error)
      }`
    );
    return CircuitState.CLOSED; // Default to CLOSED if we can't get the state
  }
}

async function main() {
  try {
    // Check circuit breaker state first

    const circuitBreakerState = await getCircuitBreakerState();

    if (circuitBreakerState === CircuitState.OPEN) {
      logger.error("❌ Circuit breaker is OPEN, skipping job execution");

      return 1;
    }

    logger.info("🚀 Starting parallel job scraper...");

    // Check if we're in test mode
    const isTestMode =
      process.env.TEST_MODE === "true" || process.argv.includes("--test-mode");

    if (isTestMode) {
      logger.info(
        "🧪 Running in test mode - simulating work without Redis locking"
      );

      // Simulate some work without Redis locking
      logger.info("⏳ Simulating work for 5 seconds...");
      await new Promise((resolve) => setTimeout(resolve, 5000));

      logger.info("✅ Test completed successfully");
    } else {
      // Normal operation - run directly without Redis locking since this is a standalone scraper
      logger.info("🚀 Running parallel job scraper without Redis locking");
      await runParallelJobScraper();
    }

    logger.info("✅ Parallel job scraper completed successfully");
    return 0;
  } catch (error) {
    logger.error("❌ Parallel job scraper failed:", error);
    return 1;
  }
}

// Run the job if this file is executed directly
if (require.main === module) {
  logger.info("Running parallelJobScraper directly...");
  main()
    .then((exitCode) => {
      process.exit(exitCode);
    })
    .catch(async (error) => {
      logger.error("Unhandled error in main:", error);

      // Check if the error is related to resource constraints or proxy issues
      if (
        error instanceof Error &&
        (error.message.includes("Proxy is down") ||
          error.message.includes("Failed to create any workers") ||
          error.message.includes("resource constraints") ||
          error.message.includes("Circuit breaker"))
      ) {
        // Determine the specific issue for better error reporting
        const isResourceIssue =
          error.message.includes("resource constraints") ||
          error.message.includes("Circuit breaker");

        const errorType = isResourceIssue
          ? "CRITICAL ERROR: System resources constrained"
          : "CRITICAL ERROR: Proxy service is down or unreachable";

        console.error(`💥 ${errorType}. Switching to fastjob without proxy.`);
        console.error(`❌ Error details: ${error.message}`);

        // Try to free up memory before proceeding
        if (global.gc) {
          logger.info(`🧹 Running garbage collection before fallback...`);
          global.gc();
          const memoryUsage =
            ((os.totalmem() - os.freemem()) / os.totalmem()) * 100;
          logger.info(`🧠 Memory after GC: ${memoryUsage.toFixed(2)}%`);
        }

        // Send appropriate email notification
        const notificationType = isResourceIssue
          ? EmailNotificationType.SYSTEM_OVERLOAD
          : EmailNotificationType.PROXY_DOWN;

        logger.info(
          `📧 Sending email notification about ${
            isResourceIssue ? "resource constraints" : "proxy being down"
          }...`
        );
        const emailSent = await sendEmailNotification(notificationType, {
          error: error.message,
          timestamp: new Date().toISOString(),
          memoryUsage: `${(
            ((os.totalmem() - os.freemem()) / os.totalmem()) *
            100
          ).toFixed(2)}%`,
          cpuUsage: `${((os.loadavg()[0] / os.cpus().length) * 100).toFixed(
            2
          )}%`,
        });

        if (emailSent) {
          logger.info(`✅ Email notification was sent successfully`);
        } else {
          logger.error(`❌ Failed to send email notification`);
        }

        // Wait a bit to allow system resources to stabilize
        if (isResourceIssue) {
          logger.info(
            `⏳ Waiting 30 seconds for system resources to stabilize...`
          );
          await new Promise((resolve) => setTimeout(resolve, 30000));

          // Check resources again
          const memoryUsage =
            ((os.totalmem() - os.freemem()) / os.totalmem()) * 100;
          const cpuUsage = (os.loadavg()[0] / os.cpus().length) * 100;
          logger.info(
            `🧠 Current resources after waiting: Memory: ${memoryUsage.toFixed(
              2
            )}%, CPU: ${cpuUsage.toFixed(2)}%`
          );

          // If resources are still constrained, exit with a specific code
          if (memoryUsage > 80 || cpuUsage > 80) {
            logger.error(
              `❌ System resources still constrained, exiting with code 78`
            );
            process.exit(78); // Use a specific exit code for resource constraints
          }
        }

        // Run fastjob without proxy, starting from where we left off
        console.log("🔄 Running fastjob without proxy as fallback...");
        try {
          // We no longer need to get the last processed occupation index and title from the database

          // We no longer store occupationTitle in metadata
          const lastOccupationTitle = null;

          console.log(
            `💾 Starting fastjob from occupation: ${
              lastOccupationTitle || "beginning"
            }`
          );

          // Use reduced resource settings for fastjob
          await runFastJobScraper({
            useProxy: false,
            startFromOccupationTitle: lastOccupationTitle || undefined,
            maxConcurrency: 1, // Reduce concurrency to minimize resource usage
          });

          // Sync progress after fastJobScraper completes
          await syncProgressWithFastJobScraper();
        } catch (error) {
          console.error("❌ Error running fastjob:", error);

          // If even the fallback fails, this is a serious issue
          await sendEmailNotification(EmailNotificationType.SYSTEM_OVERLOAD, {
            error: error instanceof Error ? error.message : String(error),
            scraper: "fastJobScraper (fallback)",
            timestamp: new Date().toISOString(),
            message:
              "Even the fallback scraper failed, indicating severe system issues",
          });
        }
      } else {
        // Send email notification about general scraper error
        await sendEmailNotification(EmailNotificationType.PROXY_DOWN, {
          error: error instanceof Error ? error.message : String(error),
          scraper: "parallelJobScraper",
          timestamp: new Date().toISOString(),
        });
      }
    });
}

// Add this function to sync progress between scrapers
async function syncProgressWithFastJobScraper() {
  try {
    // Get the latest progress from both scrapers
    const parallelProgress = await (
      await prisma
    ).scrapeProgress.findFirst({
      where: { type: "parallelJobScraper" },
      orderBy: { updatedAt: "desc" },
    });

    const fastJobProgress = await (
      await prisma
    ).scrapeProgress.findFirst({
      where: { type: "fastJobScraper" },
      orderBy: { updatedAt: "desc" },
    });

    if (!parallelProgress || !fastJobProgress) {
      logger.info(
        "💾 Cannot sync progress: One or both progress records not found"
      );
      return;
    }

    // Parse metadata
    let parallelMetadata = {};
    let fastJobMetadata: {
      cityName?: string;
      stateCode?: string;
      occupationTitle?: string;
    } = {};

    try {
      parallelMetadata = JSON.parse(parallelProgress.metadata || "{}");
      fastJobMetadata = JSON.parse(fastJobProgress.metadata || "{}");
    } catch (e) {
      logger.warn(`⚠️ Error parsing metadata during sync: ${e}`);
    }

    // Determine which has the highest city index for the same city
    const useParallelProgress =
      parallelProgress.lastCityIndex !== null &&
      fastJobProgress.lastCityIndex !== null &&
      parallelProgress.lastCityIndex >= fastJobProgress.lastCityIndex;

    // Use the highest occupation index
    const highestOccupationIndex = Math.max(
      parallelProgress.lastOccupationIndex || 0,
      fastJobProgress.lastOccupationIndex || 0
    );

    // Update the parallel job scraper record with the highest values
    await (
      await prisma
    ).scrapeProgress.update({
      where: { id: parallelProgress.id },
      data: {
        lastOccupationIndex: highestOccupationIndex,
        lastCityIndex: useParallelProgress
          ? parallelProgress.lastCityIndex
          : fastJobProgress.lastCityIndex,
        metadata: JSON.stringify({
          ...parallelMetadata,
          syncedWithFastJobScraper: true,
          syncTimestamp: new Date().toISOString(),
          fastJobLastCity: fastJobMetadata.cityName,
          fastJobLastState: fastJobMetadata.stateCode,
          fastJobLastOccupation: fastJobMetadata.occupationTitle,
        }),
        updatedAt: new Date(),
      },
    });

    logger.info(
      `💾 Synced progress between parallelJobScraper and fastJobScraper`
    );
  } catch (error) {
    logger.error(`❌ Error syncing progress between scrapers:`, error);
  }
}

// Using the central withJobLock utility from redisJobLock.ts instead of custom implementation
async function runParallelJobScraper() {
  // Initialize Prisma client
  prisma = await getPrismaClient("web");
  logger.info("✅ Prisma client initialized");

  // Check system resources before initializing anything
  const initialMemoryUsage =
    ((os.totalmem() - os.freemem()) / os.totalmem()) * 100;
  const cpuCount = os.cpus().length;
  const loadAvg = os.loadavg()[0]; // 1 minute load average
  const initialCpuUsage = (loadAvg / cpuCount) * 100;

  logger.info(
    `🧠 Initial system resources - Memory: ${initialMemoryUsage.toFixed(
      2
    )}%, CPU: ${initialCpuUsage.toFixed(2)}%`
  );

  // If resources are already critically constrained, exit early
  // Increased threshold from 80% to 90% to allow operation in production environments
  if (initialMemoryUsage > 90 || initialCpuUsage > 90) {
    logger.error(
      `❌ Critical resource constraints at startup - Memory: ${initialMemoryUsage.toFixed(
        2
      )}%, CPU: ${initialCpuUsage.toFixed(2)}%`
    );

    // Try to free up memory
    if (global.gc) {
      logger.info(`🧹 Running garbage collection before aborting...`);
      global.gc();
      const postGCMemory =
        ((os.totalmem() - os.freemem()) / os.totalmem()) * 100;
      logger.info(
        `🧠 Memory after GC: ${postGCMemory.toFixed(
          2
        )}% (was ${initialMemoryUsage.toFixed(2)}%)`
      );

      // If resources are still critically constrained after GC, exit
      // Increased threshold from 80% to 90% to allow operation in production environments
      if (postGCMemory > 90) {
        // Send notification about resource constraints
        await sendEmailNotification(EmailNotificationType.SYSTEM_OVERLOAD, {
          timestamp: new Date().toISOString(),
          message:
            "Job scraper aborted due to critical resource constraints at startup",
          memoryUsage: `${postGCMemory.toFixed(2)}%`,
          cpuUsage: `${initialCpuUsage.toFixed(2)}%`,
        }).catch((err) =>
          logger.error(`Failed to send resource constraint email: ${err}`)
        );

        throw new Error(
          "Critical resource constraints at startup - aborting job scraper"
        );
      }
    } else {
      // No GC available, exit immediately
      await sendEmailNotification(EmailNotificationType.SYSTEM_OVERLOAD, {
        timestamp: new Date().toISOString(),
        message:
          "Job scraper aborted due to critical resource constraints at startup",
        memoryUsage: `${initialMemoryUsage.toFixed(2)}%`,
        cpuUsage: `${initialCpuUsage.toFixed(2)}%`,
      }).catch((err) =>
        logger.error(`Failed to send resource constraint email: ${err}`)
      );

      throw new Error(
        "Critical resource constraints at startup - aborting job scraper"
      );
    }
  }

  // Create a global batch queue that will be shared between batch creation and workers
  const batchQueue: JobBatch[] = [];
  // Create a results array to store the results of batch processing
  const results: Awaited<ReturnType<typeof processJobBatch>>[] = [];
  // Track whether batch creation is complete
  let batchCreationComplete = false;
  // Track active workers
  let activeWorkers = 0;

  const pendingCityBatchIds = new Set<string>();

  // Use the shared circuit breaker instead of creating a new one
  const circuitBreaker = getSharedCircuitBreaker();

  try {
    // Initialize progress tracker
    const progressTracker = new ProgressTracker("parallelJobScraper");
    const progress = await progressTracker.getLatestProgress();

    // We'll use this progress record for all our tracking
    let lastProgress = null;

    if (progress) {
      try {
        // If we have metadata as a string, parse it
        if (progress.metadata) {
          try {
            const metadata = JSON.parse(progress.metadata);
            lastProgress = {
              lastOccupationIndex: progress.lastOccupationIndex,
              lastCityIndex: progress.lastCityIndex,
              metadata,
              sourceType: progress.type, // Track which type of record we found
            };
            logger.info(
              `💾 Loaded metadata from progress record: ${JSON.stringify(
                metadata
              )}`
            );
          } catch (parseError) {
            // If parsing fails, use the raw data
            lastProgress = {
              lastOccupationIndex: progress.lastOccupationIndex,
              lastCityIndex: progress.lastCityIndex,
              sourceType: progress.type,
            };
            logger.warn(`⚠️ Error parsing metadata: ${parseError}`);
          }
        } else {
          // If no metadata, just use the basic fields
          lastProgress = {
            lastOccupationIndex: progress.lastOccupationIndex,
            lastCityIndex: progress.lastCityIndex,
            sourceType: progress.type,
          };
          logger.info(
            `💾 No metadata found in progress record, using basic fields`
          );
        }

        logger.info(
          `💾 Found progress record (${
            lastProgress.sourceType
          }): ${JSON.stringify(lastProgress)}`
        );
      } catch (error) {
        logger.error(`❌ Error processing progress record:`, error);
      }
    } else {
      logger.info(`💾 No progress record found, starting fresh`);
    }

    console.log("Starting parallelJobScraper function");
    logger.info("🚀 Starting parallel job scraper...");

    // Track start time for performance monitoring
    const startTime = performance.now();

    // Initialize the location cache for city name matching
    logger.info("🌍 Initializing location cache for city name matching...");
    await initLocationCache();

    // Check system resources before initializing worker pool
    const memoryUsage = ((os.totalmem() - os.freemem()) / os.totalmem()) * 100;
    const cpuCount = os.cpus().length;
    const loadAvg = os.loadavg()[0]; // 1 minute load average
    const cpuUsage = (loadAvg / cpuCount) * 100;

    logger.info(
      `🧠 Current system resources before worker initialization - Memory: ${memoryUsage.toFixed(
        2
      )}%, CPU: ${cpuUsage.toFixed(2)}%`
    );

    // If resources are already constrained, reduce initial worker count
    let initialWorkerCount = Math.min(MAX_WORKERS, os.cpus().length - 1);
    // Increased threshold from 70% to 85% to allow operation in production environments
    if (memoryUsage > 85 || cpuUsage > 85) {
      const reducedCount = Math.max(1, Math.floor(initialWorkerCount / 2));
      logger.warn(
        `⚠️ High resource usage detected before initialization - reducing initial worker count from ${initialWorkerCount} to ${reducedCount}`
      );
      initialWorkerCount = reducedCount;

      // Try to free up memory before proceeding
      if (global.gc) {
        logger.info(
          `🧹 Running garbage collection before worker initialization...`
        );
        global.gc();
        const postGCMemory =
          ((os.totalmem() - os.freemem()) / os.totalmem()) * 100;
        logger.info(
          `🧠 Memory after GC: ${postGCMemory.toFixed(
            2
          )}% (was ${memoryUsage.toFixed(2)}%)`
        );
      }
    }

    // Initialize the worker pool with improved fingerprinting, proxy rotation, and more conservative settings
    const workerPool = new WorkerPool({
      maxWorkers: initialWorkerCount,
      headless: HEADLESS,
      slowMo: 20, // Increased from 10ms to 20ms for better stability
      recycleThreshold: 5, // Reduced from 10 to 5 jobs to prevent memory leaks
    });

    // Start memory monitoring with worker pool reference for graceful shutdown
    // Check if we're running as part of the scheduler (multiple jobs might be running)
    const isRunningFromScheduler =
      process.env.RUNNING_FROM_SCHEDULER === "true";
    const memoryMonitorInterval = monitorMemory(
      30000,
      workerPool,
      isRunningFromScheduler,
      circuitBreaker
    ); // Check memory every 30 seconds

    logger.info(
      `🚀 Initializing worker pool with improved fingerprinting and proxy rotation...`
    );

    // Check if circuit breaker is already open before attempting to initialize workers
    if (!circuitBreaker.isClosed()) {
      logger.warn(
        `⚠️ Circuit breaker is ${circuitBreaker.getState()}, skipping worker initialization`
      );
      throw new Error(
        `Worker initialization aborted: Circuit breaker is ${circuitBreaker.getState()}`
      );
    }

    try {
      await workerPool.initialize();

      // Check if we have at least one worker
      if (workerPool.getTotalWorkerCount() === 0) {
        logger.error(`❌ Failed to initialize any workers`);
        throw new Error("Failed to initialize any workers");
      }

      logger.info(
        `✅ Worker pool initialized with ${workerPool.getTotalWorkerCount()} workers`
      );
    } catch (error: unknown) {
      // Record error in circuit breaker
      circuitBreaker.recordError();

      // Convert to Error type if it's not already
      const err = error instanceof Error ? error : new Error(String(error));

      logger.error(`❌ Error initializing worker pool: ${err.message}`);

      // If this is a resource issue, try to recover
      if (
        err.message.includes("Timeout") ||
        err.message.includes("Failed to create any workers") ||
        err.message.includes("browser") ||
        err.message.includes("Circuit breaker")
      ) {
        // Try to free up memory
        if (global.gc) {
          logger.info(
            `🧹 Running garbage collection after worker initialization failure...`
          );
          global.gc();
        }

        // Throw a more specific error for the fallback mechanism
        throw new Error(
          "Worker initialization failed due to resource constraints"
        );
      }

      throw err;
    }

    // Start worker threads that will process batches as they become available
    const maxWorkers = workerPool.getTotalWorkerCount();
    let completedbatchQueue = 0;
    let successfulbatchQueue = 0;
    let totalJobsFound = 0;
    let totalJobsSaved = 0;

    // Start worker threads that will process batches as they become available
    const workerPromises = [];

    for (let i = 0; i < maxWorkers; i++) {
      const promise = (async (workerId) => {
        activeWorkers++;

        try {
          while (!batchCreationComplete || batchQueue.length > 0) {
            // Check circuit breaker before processing a batch
            if (!circuitBreaker.isClosed()) {
              // Circuit is open or half-open, pause processing
              logger.info(
                `⏸️ [Worker ${workerId}] Paused due to circuit breaker (${circuitBreaker.getState()})`
              );
              await delay(5000); // Wait 5 seconds before checking again
              continue;
            }

            const batch = batchQueue.shift();
            if (!batch) {
              if (!batchCreationComplete) await delay(1000);
              continue;
            }

            logger.info(
              `🚀 [Worker ${workerId}] Processing batch ${batch.batchId}...`
            );

            try {
              const result = await processJobBatch(workerPool, batch);
              results.push(result);

              // Remove the batch from the pending set (to track per-city progress)
              if (result.batchId) {
                pendingCityBatchIds.delete(result.batchId);
              }

              completedbatchQueue++;
              if (result.success) {
                successfulbatchQueue++;
                totalJobsFound += result.jobsFound || 0;
                totalJobsSaved += result.jobsSaved || 0;
              }
            } catch (err) {
              // Record error in circuit breaker
              circuitBreaker.recordError();

              logger.error(
                `❌ Error processing batch in worker ${workerId}`,
                err
              );
              pendingCityBatchIds.delete(batch.batchId); // Still needed so city moves on
              continue;
            }
          }
        } finally {
          activeWorkers--;
          logger.info(`👋 Worker ${workerId} finished.`);
        }
      })(i);

      workerPromises.push(promise);
    }

    try {
      // Get US states - for now, just use California like in the original fastJobScraper
      const stateId = await (
        await prisma
      ).state.findFirst({
        where: { name: "California" },
      });

      if (!stateId) {
        logger.error(
          "❌ Could not find the specified state in the state table"
        );
        await workerPool.shutdown();
        return;
      }

      // Get cities for the selected state
      const cities = (await (
        await prisma
      ).city.findMany({
        where: {
          stateId: stateId.id,
        },
        select: {
          id: true,
          name: true,
          state: {
            select: {
              id: true,
              code: true,
              name: true,
            },
          },
        },
      })) as City[];

      if (cities.length === 0) {
        logger.error("❌ No cities found for the selected states");
        await workerPool.shutdown();
        return;
      }

      logger.info(`🌎 Found ${cities.length} cities in ${stateId.name}`);

      // Get occupations from the database
      const occupations = (await (
        await prisma
      ).occupations.findMany({
        select: {
          id: true,
          title: true,
        },
        orderBy: {
          title: "asc",
        },
      })) as Occupation[];

      if (occupations.length === 0) {
        logger.error("❌ No occupations found in the database");
        await workerPool.shutdown();
        return;
      }

      logger.info(`💼 Found ${occupations.length} occupations`);

      // If we don't have a progress record yet, create one
      if (!lastProgress) {
        try {
          // Create a fresh record to begin tracking progress
          const progressRecord = await (
            await prisma
          ).scrapeProgress.create({
            data: {
              id: `progress_${Date.now()}`,
              type: "parallelJobScraper",
              lastCityIndex: 0,
              lastOccupationIndex: 0,
              metadata: JSON.stringify({
                createdAt: new Date().toISOString(),
                message: "Initialized fresh progress for parallelJobScraper",
              }),
            },
          });

          logger.info(
            `💾 Created new progress record with ID: ${progressRecord.id}`
          );

          // Initialize lastProgress with the new record
          lastProgress = {
            lastOccupationIndex: 0,
            lastCityIndex: 0,
            metadata: {
              createdAt: new Date().toISOString(),
              message: "Initialized fresh progress for parallelJobScraper",
            },
            sourceType: "parallelJobScraper",
          };
        } catch (error) {
          logger.error(`❌ Error creating progress record:`, error);
        }
      }

      let currentCityIndex = lastProgress?.lastCityIndex ?? 0;
      // Get the lastOccupationIndex from the progress record
      let lastOccupationIndex: number | null = null;

      if (
        lastProgress?.lastOccupationIndex !== undefined &&
        lastProgress?.lastOccupationIndex !== null
      ) {
        // Otherwise use the lastOccupationIndex
        const parsedIndex = Number(lastProgress.lastOccupationIndex);

        // Check if the parsed value is NaN and handle it
        if (isNaN(parsedIndex)) {
          logger.warn(`⚠️ lastOccupationIndex is NaN, setting to null`);
          lastOccupationIndex = null;
        } else {
          lastOccupationIndex = parsedIndex;
          logger.info(`💾 Using lastOccupationIndex: ${lastOccupationIndex}`);
        }
      } else {
        // Default to null if nothing is available
        // This ensures we start from the beginning of the city
        lastOccupationIndex = null;
        logger.info(
          `💾 No occupation index found, starting from the beginning of city ${currentCityIndex}`
        );
      }

      if (lastProgress) {
        if (
          lastProgress.lastCityIndex !== undefined &&
          lastProgress.lastCityIndex !== null
        ) {
          const index = lastProgress.lastCityIndex;
          if (index >= 0 && index < cities.length) {
            currentCityIndex = index;
            logger.info(
              `🔄 Resuming from city index: ${currentCityIndex} (${cities[currentCityIndex].name}), occupation index: ${lastOccupationIndex}`
            );
          } else {
            logger.warn(`⚠️ Invalid city index ${index}, starting from 0`);
          }
        } else {
          logger.info("💾 DEBUG: No lastCityIndex in progress record");
        }
      } else {
        logger.info(
          `🔄 Starting from scratch — first city: ${cities[0].name}, occupation: ${occupations[0].title}`
        );
      }

      // Inside City Loop
      while (currentCityIndex < cities.length) {
        const city = cities[currentCityIndex];
        const completedThisCity = new Set<string>(
          lastProgress?.metadata?.completedOccupationIds || []
        );

        logger.info(
          `🏢 Processing city: ${city.name}, ${city.state.code}, current occupation index: ${lastOccupationIndex}`
        );

        // Determine the starting occupation index
        let startOccupationIndex = 0;

        if (lastOccupationIndex !== null && !isNaN(lastOccupationIndex)) {
          // Try to find the occupation by ID first
          const idxById = occupations.findIndex(
            (o) => Number(o.id) === Number(lastOccupationIndex)
          );

          if (idxById >= 0) {
            // Found by ID - start from the next occupation
            startOccupationIndex = idxById + 1;
            logger.info(
              `🔁 Resuming from occupation index ${startOccupationIndex} (found by ID: ${occupations[idxById].title})`
            );

            // Mark the current occupation as completed
            completedThisCity.add(String(occupations[idxById].id));
            logger.info(
              `✅ Marked occupation ${occupations[idxById].title} (ID: ${occupations[idxById].id}) as completed`
            );
          } else {
            // If not found by ID, try to use the lastOccupationIndex directly as an index
            // This handles the case where we stored the index rather than the ID
            const numIndex = Number(lastOccupationIndex);
            if (
              !isNaN(numIndex) &&
              numIndex >= 0 &&
              numIndex < occupations.length
            ) {
              startOccupationIndex = numIndex + 1;

              // Mark the current occupation as completed
              if (numIndex >= 0 && numIndex < occupations.length) {
                completedThisCity.add(String(occupations[numIndex].id));
                logger.info(
                  `✅ Marked occupation ${occupations[numIndex].title} (ID: ${occupations[numIndex].id}) as completed`
                );
              }

              if (startOccupationIndex >= occupations.length) {
                // If we've reached the end of occupations, move to the next city
                logger.info(
                  `🔄 Reached end of occupations list for city ${currentCityIndex}, moving to next city`
                );
                currentCityIndex++;
                startOccupationIndex = 0;

                // IMPORTANT: Set lastOccupationIndex to null, not 0
                lastOccupationIndex = null;
                logger.info(
                  `💾 Resetting lastOccupationIndex to null for next city`
                );

                // Skip this city iteration and start with the next city
                if (currentCityIndex < cities.length) {
                  logger.info(
                    `🔁 Moving to city: ${cities[currentCityIndex].name}`
                  );

                  // Save progress with the updated city index before continuing
                  await updateProgressRecord(
                    currentCityIndex,
                    null,
                    {
                      completedbatchQueue: 0,
                      successfulbatchQueue: 0,
                      totalbatchQueue: 0,
                      totalJobsFound: 0,
                      totalJobsSaved: 0,
                      timestamp: new Date().toISOString(),
                      // occupationId removed
                    },
                    new Set<string>() // Reset completedThisCity for the new city
                  );

                  continue;
                } else {
                  logger.info(
                    `🔁 Reached the end of all cities, starting over`
                  );
                  currentCityIndex = 0;

                  // Save progress with the reset city index
                  await updateProgressRecord(
                    0,
                    null,
                    {
                      completedbatchQueue: 0,
                      successfulbatchQueue: 0,
                      totalbatchQueue: 0,
                      totalJobsFound: 0,
                      totalJobsSaved: 0,
                      timestamp: new Date().toISOString(),
                      // occupationId removed
                    },
                    new Set<string>() // Reset completedThisCity for the new city
                  );
                }
              } else {
                logger.info(
                  `🔁 Resuming from occupation index ${startOccupationIndex} (using stored index)`
                );
              }
            } else {
              logger.info(
                `💾 Could not find occupation with ID ${lastOccupationIndex}, starting from the beginning of this city`
              );
            }
          }
          // Don't reset lastOccupationIndex to 0 - we need to keep it for the next iteration
          // Instead, store it in the metadata so we can resume from the correct point
          // We'll update it after processing batchQueue
        }

        // Log the current state of completedThisCity
        logger.info(
          `✅ Currently have ${completedThisCity.size} completed occupations for city ${city.name}`
        );

        // ——————————————————————————————————————————————
        // slice‑by‑index approach: workers pull the next chunk atomically
        // ——————————————————————————————————————————————
        // before spawning workers:
        const totalOcc = occupations.length;
        let nextIdx = startOccupationIndex;

        // launch one async worker loop per browser
        const workerPromises: Promise<void>[] = [];
        for (let workerId = 0; workerId < maxWorkers; workerId++) {
          workerPromises.push(
            (async () => {
              while (true) {
                // grab the next slice atomically
                const myStart = nextIdx;
                if (myStart >= totalOcc) break;
                nextIdx += MAX_OCCUPATIONS_PER_BATCH;

                // build our batch slice
                const slice = occupations
                  .slice(
                    myStart,
                    Math.min(myStart + MAX_OCCUPATIONS_PER_BATCH, totalOcc)
                  )
                  .filter((o) => !completedThisCity.has(String(o.id)));
                if (slice.length === 0) {
                  // nothing to do here, go grab the next slice
                  continue;
                }

                // create and process
                const batch: JobBatch = {
                  batchId: uuidv4(),
                  cityId: city.id,
                  cityName: city.name,
                  cityIndex: currentCityIndex,
                  stateId: city.state.id,
                  stateCode: city.state.code || "",
                  stateName: city.state.name,
                  occupations: slice.map((o) => ({ id: o.id, title: o.title })),
                  workerIndex: workerId,
                };

                logger.info(
                  `🚀 [Worker ${workerId}] Processing ${slice.length} occupations…`
                );
                const result = await processJobBatch(workerPool, batch);

                // mark completed IDs & update counters
                if (result.success && result.completedOccupationIds) {
                  for (const id of result.completedOccupationIds) {
                    completedThisCity.add(id);
                  }
                }

                // persist your progress immediately:
                await saveProgress(
                  /* lastOccIdx */ myStart + slice.length - 1,
                  /* cityIdx */ currentCityIndex,
                  /* metadata */ {
                    timestamp: new Date().toISOString(),
                    lastBatchId: batch.batchId,
                  },
                  /* completedThisCity */ completedThisCity
                );
              }

              logger.info(`👋 Worker ${workerId} done for city ${city.name}`);
            })()
          );
        }

        // wait until every slice is done
        await Promise.all(workerPromises);

        // at this point you know `completedThisCity` covers all occupations

        // Check if all occupations for this city have been processed
        const currentOccupationIds = occupations.map((o) => String(o.id));
        const allOccupationsCovered = currentOccupationIds.every((id) =>
          completedThisCity.has(id)
        );

        // Log the current coverage
        const completedCount = currentOccupationIds.filter((id) =>
          completedThisCity.has(id)
        ).length;
        logger.info(
          `✅ City ${city.name} coverage: ${completedCount}/${currentOccupationIds.length} occupations completed`
        );

        if (allOccupationsCovered) {
          logger.info(
            `✅ All occupations for city ${city.name} processed. Moving to next city.`
          );
          currentCityIndex++;

          // IMPORTANT: Set lastOccupationIndex to null, not 0
          // This ensures we start from the beginning of the next city
          // Setting to 0 could be confused with occupation ID 0
          lastOccupationIndex = null;

          logger.info(
            `💾 Moving to city index ${currentCityIndex} and resetting occupation index to null`
          );

          // Save progress with the updated city index
          await updateProgressRecord(
            currentCityIndex,
            null, // Use null instead of 0 for the occupation index
            {
              completedbatchQueue: 0,
              successfulbatchQueue: 0,
              totalbatchQueue: 0,
              totalJobsFound: 0,
              totalJobsSaved: 0,
              timestamp: new Date().toISOString(),
              // occupationId removed
            },
            new Set<string>() // Reset completedThisCity for the new city
          );
        } else {
          logger.info(
            `🔁 Not all occupations completed for ${city.name}, will resume remaining ones.`
          );
        }
      }

      logger.info(`📦 Created ${batchQueue.length} batchQueue of work`);

      // Mark batch creation as complete
      batchCreationComplete = true;

      await Promise.all(workerPromises);

      logger.info(`✅ All workers have completed processing`);

      // Calculate final statistics
      let completedbatchQueue = results.length;
      let successfulbatchQueue = results.filter((r) => r.success).length;
      let totalJobsFound = results.reduce(
        (sum, r) => sum + (r.jobsFound || 0),
        0
      );
      let totalJobsSaved = results.reduce(
        (sum, r) => sum + (r.jobsSaved || 0),
        0
      );

      // Calculate and log the total execution time
      const endTime = performance.now();
      const durationMs = endTime - startTime;
      const executionTimeMinutes = (durationMs / 1000 / 60).toFixed(2);

      // Log job statistics in standardized format for easier parsing
      logger.jobStats({
        jobType: "parallelJobScraper",
        processed: totalJobsFound,
        succeeded: totalJobsSaved,
        failed: totalJobsFound - totalJobsSaved,
        duration: durationMs,
        details: {
          batchesProcessed: completedbatchQueue,
          batchesSuccessful: successfulbatchQueue,
          batchesFailed: completedbatchQueue - successfulbatchQueue,
          successRate:
            totalJobsFound > 0
              ? ((totalJobsSaved / totalJobsFound) * 100).toFixed(1) + "%"
              : "N/A",
          executionTimeMinutes: executionTimeMinutes,
          memory: {
            rss: Math.round(process.memoryUsage().rss / 1024 / 1024),
            heapUsed: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
            heapTotal: Math.round(
              process.memoryUsage().heapTotal / 1024 / 1024
            ),
          },
        },
      });

      logger.info(
        `📊 Final results: ${completedbatchQueue} batches processed, ${successfulbatchQueue} successful, ${totalJobsFound} jobs found, ${totalJobsSaved} jobs saved`
      );
      logger.info(`⏱️ Total execution time: ${executionTimeMinutes} minutes`);

      // Function to process batches from the queue is now defined above

      // Function to process batches from the queue
    } catch (error) {
      logger.error(`❌ Error in runParallelJobScraper:`, error);

      // Log error with standardized job statistics
      const endTime = performance.now();
      const durationMs = endTime - startTime;

      logger.jobStats({
        jobType: "parallelJobScraper",
        processed: 0,
        succeeded: 0,
        failed: 1, // The job itself failed
        duration: durationMs,
        details: {
          error: error instanceof Error ? error.message : String(error),
          errorStack: error instanceof Error ? error.stack : undefined,
          memory: {
            rss: Math.round(process.memoryUsage().rss / 1024 / 1024),
            heapUsed: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
            heapTotal: Math.round(
              process.memoryUsage().heapTotal / 1024 / 1024
            ),
          },
        },
      });
    } finally {
      // Clean up memory monitoring
      if (memoryMonitorInterval) {
        clearInterval(memoryMonitorInterval);
        logger.info(`🧠 Memory monitoring stopped`);
      }

      // Stop the circuit breaker monitoring
      circuitBreaker.stopMonitoring();
      logger.info("🛑 Circuit breaker monitoring stopped");

      // Force garbage collection before exiting
      if (global.gc) {
        logger.info(`🧹 Running garbage collection before exit`);
        global.gc();
      }

      // Lock is now handled by the withJobLock utility in the main function

      // Clean up resources
      try {
        // Close Redis connection
        await redis.quit().catch((err) => {
          logger.error("Error closing Redis connection:", err);
        });

        // Disconnect Prisma
        await (await prisma).$disconnect().catch((err: any) => {
          logger.error("Error disconnecting Prisma:", err);
        });
      } catch (cleanupError) {
        logger.error("❌ Error during cleanup:", cleanupError);
      }
    }
  } catch (error) {
    logger.error(`❌ Error in runParallelJobScraper:`, error);

    // Record error in circuit breaker
    circuitBreaker.recordError();

    // Log error with standardized job statistics
    logger.jobStats({
      jobType: "parallelJobScraper",
      processed: 0,
      succeeded: 0,
      failed: 1, // The job itself failed
      duration: 0, // We don't have a startTime in this scope
      details: {
        error: error instanceof Error ? error.message : String(error),
        errorStack: error instanceof Error ? error.stack : undefined,
        memory: {
          rss: Math.round(process.memoryUsage().rss / 1024 / 1024),
          heapUsed: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
          heapTotal: Math.round(process.memoryUsage().heapTotal / 1024 / 1024),
        },
        circuitBreakerState: circuitBreaker.getState(),
      },
    });
  } finally {
    // Lock is now handled by the withJobLock utility in the main function

    // Clean up resources
    try {
      // Close Redis connection
      await redis.quit().catch((err) => {
        logger.error("Error closing Redis connection:", err);
      });

      // Disconnect Prisma
      await (await prisma).$disconnect().catch((err: Error) => {
        logger.error("Error disconnecting Prisma:", err);
      });
    } catch (cleanupError) {
      logger.error("❌ Error during cleanup:", cleanupError);
    }
  }
}

// When saving progress after processing batchQueue
async function saveProgress(
  lastOccupationIndex: number | null,
  cityIndex: number | null,
  metadata: any,
  completedThisCity: Set<string>
) {
  try {
    const progressTracker = new ProgressTracker("parallelJobScraper");

    // Get occupations to check if we need to increment city index
    const occupations = await (
      await prisma
    ).occupations.findMany({
      select: {
        id: true,
        title: true,
      },
      orderBy: {
        title: "asc",
      },
    });

    logger.info(`💾 DEBUG: Total occupations found: ${occupations.length}`);
    logger.info(
      `✅ City ${cityIndex} coverage: ${completedThisCity.size}/${occupations.length} occupations`
    );

    // Use the provided occupation index
    let validOccupationIndex: number | null = null;

    if (lastOccupationIndex === null) {
      validOccupationIndex = null;
      logger.info(
        `💾 Using null occupation index to start from the beginning of the city`
      );
    } else if (isNaN(Number(lastOccupationIndex))) {
      // If it's not a number, try to find the occupation by ID
      const occupationId = String(lastOccupationIndex);
      const idxById = occupations.findIndex(
        (o: { id: any }) => String(o.id) === occupationId
      );

      if (idxById >= 0) {
        validOccupationIndex = idxById;
        logger.info(
          `💾 Found occupation index ${validOccupationIndex} for ID ${occupationId}`
        );
      } else {
        // If we can't find the occupation by ID, use a small index to avoid getting stuck
        validOccupationIndex = 0;
        logger.warn(
          `⚠️ Could not find occupation with ID ${occupationId}, using index 0 to avoid getting stuck`
        );
      }
    } else {
      // It's a number, use it directly
      validOccupationIndex = Number(lastOccupationIndex);
      logger.info(
        `💾 Using provided occupation index: ${validOccupationIndex}`
      );
    }

    const validCityIndex = isNaN(Number(cityIndex)) ? null : Number(cityIndex);

    logger.info(
      `💾 Saving progress: occupation=${validOccupationIndex}, city=${validCityIndex}`
    );

    const occupationIdsForCity = occupations.map((o: { id: any }) =>
      String(o.id)
    );
    const isAllOccupationsCovered = occupationIdsForCity.every((id: string) =>
      completedThisCity.has(id)
    );

    let updatedCityIndex = validCityIndex;
    let updatedOccupationIndex: number | null = validOccupationIndex;

    if (isAllOccupationsCovered) {
      logger.info(
        `✅ All occupations completed for city index ${validCityIndex}, moving to next city...`
      );
      updatedCityIndex = validCityIndex !== null ? validCityIndex + 1 : 0;
      updatedOccupationIndex = null; // Use null instead of 0 to avoid confusion with occupation ID 0

      // 🔄 Ensure the set is fully populated before saving (in case we missed any in processing)
      completedThisCity.clear();
      occupationIdsForCity.forEach((id: string) => completedThisCity.add(id));
    }

    // Ensure metadata has required fields
    const validMetadata = {
      ...metadata,
      timestamp: metadata.timestamp || new Date().toISOString(),
      processedBy: "parallelJobScraper",
      jobsFound: metadata.jobsFound || 0,
      totalJobsFound: metadata.totalJobsFound || 0,
      // Store the occupation index to help with resuming
      occupationIndex: updatedOccupationIndex,
      // Store the completed occupation IDs
      completedOccupationIds: Array.from(completedThisCity),
    };

    const saved = await progressTracker.updateProgress({
      lastOccupationIndex: updatedOccupationIndex,
      lastCityIndex: updatedCityIndex,
      metadata: validMetadata,
    });

    if (saved) {
      // Verify the save was successful
      const verified = await progressTracker.verifyProgressSaved({
        lastOccupationIndex: updatedOccupationIndex,
        lastCityIndex: updatedCityIndex,
      });

      if (!verified) {
        logger.warn(
          `⚠️ Progress verification failed, attempting to update existing record directly`
        );

        try {
          // Try to find and update the existing record directly
          const existingRecord = await (
            await prisma
          ).scrapeProgress.findFirst({
            where: { type: "parallelJobScraper" },
            orderBy: { updatedAt: "desc" },
          });

          if (existingRecord) {
            await (
              await prisma
            ).scrapeProgress.update({
              where: { id: existingRecord.id },
              data: {
                lastOccupationIndex: updatedOccupationIndex,
                lastCityIndex: updatedCityIndex,
                metadata: JSON.stringify({
                  ...validMetadata,
                  recoveryAttempt: true,
                  recoveryTimestamp: new Date().toISOString(),
                }),
                updatedAt: new Date(),
              },
            });
            logger.info(
              `💾 Recovery update successful for record ID: ${existingRecord.id}`
            );
          } else {
            logger.warn(`⚠️ No existing record found for recovery update`);
          }
        } catch (recoveryError) {
          logger.error(`❌ Error during recovery update: ${recoveryError}`);
        }
      }
    } else {
      logger.error(`❌ Failed to save progress`);
    }
  } catch (error) {
    logger.error(`❌ Error in saveProgress:`, error);
  }
}

// Update the progress tracking to use lastOccupationIndex instead of lastOccupationId
async function updateProgressRecord(
  cityIndex: number,
  occupationIndex: string | number | null,
  metadata: {
    completedbatchQueue: number;
    successfulbatchQueue: number;
    totalbatchQueue: number;
    totalJobsFound: number;
    totalJobsSaved: number;
    timestamp: string;
    // occupationId removed from schema
  },
  completedThisCity: Set<string> = new Set<string>()
) {
  try {
    // Get occupations to check if we need to increment city index
    const occupations = await (
      await prisma
    ).occupations.findMany({
      select: {
        id: true,
        title: true,
      },
      orderBy: {
        title: "asc",
      },
    });

    // Determine the occupation index based on either the provided index or the ID in metadata
    let validOccupationIndex: number | null = null;

    if (occupationIndex === null) {
      // If null, keep it as null to start from the beginning of the city
      validOccupationIndex = null;
      logger.info(
        `💾 No occupation index provided, keeping as null to start from the beginning of the city`
      );
    } else if (isNaN(Number(occupationIndex))) {
      // If it's not a number, try to find the occupation by ID
      const occupationId = String(occupationIndex);
      const idxById = occupations.findIndex(
        (o: { id: any }) => String(o.id) === occupationId
      );

      if (idxById >= 0) {
        validOccupationIndex = idxById;
        logger.info(
          `💾 Found occupation index ${validOccupationIndex} for ID ${occupationId}`
        );
      } else {
        validOccupationIndex = null;
        logger.warn(
          `⚠️ Could not find occupation with ID ${occupationId}, starting from the beginning of the city`
        );
      }
    } else {
      // It's a number, use it directly
      validOccupationIndex = Number(occupationIndex);
      logger.info(
        `💾 Using provided occupation index: ${validOccupationIndex}`
      );
    }

    const validCityIndex = isNaN(Number(cityIndex)) ? null : Number(cityIndex);

    // Check if we've reached the end of occupations for this city
    let updatedCityIndex = validCityIndex;
    let updatedOccupationIndex = validOccupationIndex;

    // If we have a valid occupation index and it's the last one in the list
    if (validOccupationIndex !== null && occupations.length > 0) {
      // If we're at the last occupation, we should move to the next city
      if (validOccupationIndex >= occupations.length - 1) {
        logger.info(
          `🔄 Reached the end of occupations for city ${validCityIndex}, moving to next city`
        );
        updatedCityIndex = validCityIndex !== null ? validCityIndex + 1 : 0;
        updatedOccupationIndex = null; // Reset occupation index to null for the new city
      }
    }

    // Log the completed occupations for this city
    logger.info(
      `✅ City ${validCityIndex} coverage: ${completedThisCity.size} occupations completed`
    );

    // Check if all occupations are completed for this city
    const occupationIdsForCity = occupations.map((o: { id: any }) =>
      String(o.id)
    );
    const isAllOccupationsCovered = occupationIdsForCity.every((id: string) =>
      completedThisCity.has(id)
    );

    if (isAllOccupationsCovered) {
      logger.info(
        `✅ All occupations completed for city index ${validCityIndex}, moving to next city...`
      );
      updatedCityIndex = validCityIndex !== null ? validCityIndex + 1 : 0;
      updatedOccupationIndex = null; // Use null instead of 0 to avoid confusion with occupation ID 0
    }

    // Add completedThisCity to metadata
    const updatedMetadata = {
      ...metadata,
      completedOccupationIds: Array.from(completedThisCity),
    };

    // First check if a parallelJobScraper record exists
    const existingRecord = await (
      await prisma
    ).scrapeProgress.findFirst({
      where: { type: "parallelJobScraper" },
      orderBy: { updatedAt: "desc" },
    });

    if (existingRecord) {
      logger.info(
        `💾 Updating progress: City index ${validCityIndex} -> ${updatedCityIndex}, Occupation index ${validOccupationIndex} -> ${updatedOccupationIndex}`
      );

      // Update the existing record
      await (
        await prisma
      ).scrapeProgress.update({
        where: { id: existingRecord.id },
        data: {
          lastCityIndex: updatedCityIndex,
          lastOccupationIndex: updatedOccupationIndex,
          metadata: JSON.stringify(updatedMetadata),
          updatedAt: new Date(),
        },
      });
    } else {
      // Check if any record with this type exists (might have been created by another process)
      const anyExistingRecord = await (
        await prisma
      ).scrapeProgress.findFirst({
        where: { type: "parallelJobScraper" },
      });

      if (anyExistingRecord) {
        // If a record exists, update it instead of creating a new one
        logger.info(
          `💾 Found existing record with type parallelJobScraper, updating instead of creating new one`
        );

        await (
          await prisma
        ).scrapeProgress.update({
          where: { id: anyExistingRecord.id },
          data: {
            lastCityIndex: updatedCityIndex,
            lastOccupationIndex: updatedOccupationIndex,
            metadata: JSON.stringify(updatedMetadata),
            updatedAt: new Date(),
          },
        });
      } else {
        // Create a new record only if no record with this type exists
        logger.info(
          `💾 Creating new progress record: City index ${validCityIndex} -> ${updatedCityIndex}, Occupation index ${validOccupationIndex} -> ${updatedOccupationIndex}`
        );

        await (
          await prisma
        ).scrapeProgress.create({
          data: {
            id: `progress_${Date.now()}`,
            type: "parallelJobScraper",
            lastCityIndex: updatedCityIndex,
            lastOccupationIndex: updatedOccupationIndex,
            metadata: JSON.stringify(updatedMetadata),
          },
        });
      }
    }
  } catch (error) {
    logger.error(`❌ Error updating progress record: ${error}`);
  }
}
