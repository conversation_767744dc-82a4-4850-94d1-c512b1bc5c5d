// scraper/jobs/parallelJobScraper.ts
// Simplified parallel job scraper

import { logger } from "../utils/logger";
import {
  sendEmailNotification,
  EmailNotificationType,
} from "../utils/emailService";
import { getPrismaClient } from "../utils/prismaClient";
import { config } from "../config";
import Redis from "ioredis";
import { CircuitState } from "../utils/improvedImprovedCircuitBreaker";
import os from "os";

// Initialize Prisma client variable
let prisma: any;

// Create Redis client for circuit breaker
const redis = new Redis(config.redis.url as string);

/**
 * Get the current circuit breaker state from Redis
 */
async function getCircuitBreakerState(): Promise<CircuitState> {
  try {
    const state = await redis.get("circuit_breaker:state");
    return (state as CircuitState) || CircuitState.CLOSED;
  } catch (error) {
    logger.error(
      `❌ Error getting circuit breaker state: ${
        error instanceof Error ? error.message : String(error)
      }`
    );
    return CircuitState.CLOSED; // Default to CLOSED if we can't get the state
  }
}

/**
 * Simplified parallel job scraper - just logs a message for now
 */
async function runParallelJobScraper() {
  // Initialize Prisma client
  prisma = await getPrismaClient("web");
  logger.info("✅ Prisma client initialized");

  // Check system resources
  const totalMemory = os.totalmem();
  const freeMemory = os.freemem();
  const usedMemory = totalMemory - freeMemory;
  const memoryUsage = (usedMemory / totalMemory) * 100;

  logger.info(`🧠 System resources - Memory: ${memoryUsage.toFixed(2)}%`);

  // If resources are critically constrained, exit early
  if (memoryUsage > 90) {
    logger.error(`❌ Critical memory usage: ${memoryUsage.toFixed(2)}%`);

    await sendEmailNotification(EmailNotificationType.SYSTEM_OVERLOAD, {
      timestamp: new Date().toISOString(),
      message: "Job scraper aborted due to critical memory usage",
      memoryUsage: `${memoryUsage.toFixed(2)}%`,
    }).catch((err) =>
      logger.error(`Failed to send resource constraint email: ${err}`)
    );

    throw new Error("Critical memory usage - aborting job scraper");
  }

  logger.info("🚀 Parallel job scraper running...");

  // For now, just simulate some work
  logger.info("⏳ Simulating job scraping work...");
  await new Promise((resolve) => setTimeout(resolve, 5000));

  logger.info("✅ Parallel job scraper completed successfully");
}

/**
 * Main function to run the job with proper cleanup
 */
async function main() {
  try {
    // Check circuit breaker state first
    const circuitBreakerState = await getCircuitBreakerState();

    if (circuitBreakerState === CircuitState.OPEN) {
      logger.error("❌ Circuit breaker is OPEN, skipping job execution");
      return 1;
    }

    logger.info("🚀 Starting parallel job scraper...");

    // Check if we're in test mode
    const isTestMode =
      process.env.TEST_MODE === "true" || process.argv.includes("--test-mode");

    if (isTestMode) {
      logger.info(
        "🧪 Running in test mode - simulating work without Redis locking"
      );

      // Simulate some work without Redis locking
      logger.info("⏳ Simulating work for 5 seconds...");
      await new Promise((resolve) => setTimeout(resolve, 5000));

      logger.info("✅ Test completed successfully");
    } else {
      // Normal operation - run the simplified scraper
      logger.info("🚀 Running simplified parallel job scraper");
      await runParallelJobScraper();
    }

    logger.info("✅ Parallel job scraper completed successfully");
    return 0;
  } catch (error) {
    logger.error("❌ Parallel job scraper failed:", error);
    return 1;
  }
}

// Run the job if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  logger.info("Running parallelJobScraper directly...");
  main()
    .then((exitCode) => {
      process.exit(exitCode);
    })
    .catch(async (error) => {
      logger.error("Unhandled error in main:", error);
      process.exit(1);
    });
}

export { runParallelJobScraper };

    try {
      // Get US states - for now, just use California like in the original fastJobScraper
      const stateId = await (
        await prisma
      ).state.findFirst({
        where: { name: "California" },
      });

      if (!stateId) {
        logger.error(
          "❌ Could not find the specified state in the state table"
        );
        await workerPool.shutdown();
        return;
      }

      // Get cities for the selected state
      const cities = (await (
        await prisma
      ).city.findMany({
        where: {
          stateId: stateId.id,
        },
        select: {
          id: true,
          name: true,
          state: {
            select: {
              id: true,
              code: true,
              name: true,
            },
          },
        },
      })) as City[];

      if (cities.length === 0) {
        logger.error("❌ No cities found for the selected states");
        await workerPool.shutdown();
        return;
      }

      logger.info(`🌎 Found ${cities.length} cities in ${stateId.name}`);

      // Get occupations from the database
      const occupations = (await (
        await prisma
      ).occupations.findMany({
        select: {
          id: true,
          title: true,
        },
        orderBy: {
          title: "asc",
        },
      })) as Occupation[];

      if (occupations.length === 0) {
        logger.error("❌ No occupations found in the database");
        await workerPool.shutdown();
        return;
      }

      logger.info(`💼 Found ${occupations.length} occupations`);

      // If we don't have a progress record yet, create one
      if (!lastProgress) {
        try {
          // Create a fresh record to begin tracking progress
          const progressRecord = await (
            await prisma
          ).scrapeProgress.create({
            data: {
              id: `progress_${Date.now()}`,
              type: "parallelJobScraper",
              lastCityIndex: 0,
              lastOccupationIndex: 0,
              metadata: JSON.stringify({
                createdAt: new Date().toISOString(),
                message: "Initialized fresh progress for parallelJobScraper",
              }),
            },
          });

          logger.info(
            `💾 Created new progress record with ID: ${progressRecord.id}`
          );

          // Initialize lastProgress with the new record
          lastProgress = {
            lastOccupationIndex: 0,
            lastCityIndex: 0,
            metadata: {
              createdAt: new Date().toISOString(),
              message: "Initialized fresh progress for parallelJobScraper",
            },
            sourceType: "parallelJobScraper",
          };
        } catch (error) {
          logger.error(`❌ Error creating progress record:`, error);
        }
      }

      let currentCityIndex = lastProgress?.lastCityIndex ?? 0;
      // Get the lastOccupationIndex from the progress record
      let lastOccupationIndex: number | null = null;

      if (
        lastProgress?.lastOccupationIndex !== undefined &&
        lastProgress?.lastOccupationIndex !== null
      ) {
        // Otherwise use the lastOccupationIndex
        const parsedIndex = Number(lastProgress.lastOccupationIndex);

        // Check if the parsed value is NaN and handle it
        if (isNaN(parsedIndex)) {
          logger.warn(`⚠️ lastOccupationIndex is NaN, setting to null`);
          lastOccupationIndex = null;
        } else {
          lastOccupationIndex = parsedIndex;
          logger.info(`💾 Using lastOccupationIndex: ${lastOccupationIndex}`);
        }
      } else {
        // Default to null if nothing is available
        // This ensures we start from the beginning of the city
        lastOccupationIndex = null;
        logger.info(
          `💾 No occupation index found, starting from the beginning of city ${currentCityIndex}`
        );
      }

      if (lastProgress) {
        if (
          lastProgress.lastCityIndex !== undefined &&
          lastProgress.lastCityIndex !== null
        ) {
          const index = lastProgress.lastCityIndex;
          if (index >= 0 && index < cities.length) {
            currentCityIndex = index;
            logger.info(
              `🔄 Resuming from city index: ${currentCityIndex} (${cities[currentCityIndex].name}), occupation index: ${lastOccupationIndex}`
            );
          } else {
            logger.warn(`⚠️ Invalid city index ${index}, starting from 0`);
          }
        } else {
          logger.info("💾 DEBUG: No lastCityIndex in progress record");
        }
      } else {
        logger.info(
          `🔄 Starting from scratch — first city: ${cities[0].name}, occupation: ${occupations[0].title}`
        );
      }

      // Inside City Loop
      while (currentCityIndex < cities.length) {
        const city = cities[currentCityIndex];
        const completedThisCity = new Set<string>(
          lastProgress?.metadata?.completedOccupationIds || []
        );

        logger.info(
          `🏢 Processing city: ${city.name}, ${city.state.code}, current occupation index: ${lastOccupationIndex}`
        );

        // Determine the starting occupation index
        let startOccupationIndex = 0;

        if (lastOccupationIndex !== null && !isNaN(lastOccupationIndex)) {
          // Try to find the occupation by ID first
          const idxById = occupations.findIndex(
            (o) => Number(o.id) === Number(lastOccupationIndex)
          );

          if (idxById >= 0) {
            // Found by ID - start from the next occupation
            startOccupationIndex = idxById + 1;
            logger.info(
              `🔁 Resuming from occupation index ${startOccupationIndex} (found by ID: ${occupations[idxById].title})`
            );

            // Mark the current occupation as completed
            completedThisCity.add(String(occupations[idxById].id));
            logger.info(
              `✅ Marked occupation ${occupations[idxById].title} (ID: ${occupations[idxById].id}) as completed`
            );
          } else {
            // If not found by ID, try to use the lastOccupationIndex directly as an index
            // This handles the case where we stored the index rather than the ID
            const numIndex = Number(lastOccupationIndex);
            if (
              !isNaN(numIndex) &&
              numIndex >= 0 &&
              numIndex < occupations.length
            ) {
              startOccupationIndex = numIndex + 1;

              // Mark the current occupation as completed
              if (numIndex >= 0 && numIndex < occupations.length) {
                completedThisCity.add(String(occupations[numIndex].id));
                logger.info(
                  `✅ Marked occupation ${occupations[numIndex].title} (ID: ${occupations[numIndex].id}) as completed`
                );
              }

              if (startOccupationIndex >= occupations.length) {
                // If we've reached the end of occupations, move to the next city
                logger.info(
                  `🔄 Reached end of occupations list for city ${currentCityIndex}, moving to next city`
                );
                currentCityIndex++;
                startOccupationIndex = 0;

                // IMPORTANT: Set lastOccupationIndex to null, not 0
                lastOccupationIndex = null;
                logger.info(
                  `💾 Resetting lastOccupationIndex to null for next city`
                );

                // Skip this city iteration and start with the next city
                if (currentCityIndex < cities.length) {
                  logger.info(
                    `🔁 Moving to city: ${cities[currentCityIndex].name}`
                  );

                  // Save progress with the updated city index before continuing
                  await updateProgressRecord(
                    currentCityIndex,
                    null,
                    {
                      completedbatchQueue: 0,
                      successfulbatchQueue: 0,
                      totalbatchQueue: 0,
                      totalJobsFound: 0,
                      totalJobsSaved: 0,
                      timestamp: new Date().toISOString(),
                      // occupationId removed
                    },
                    new Set<string>() // Reset completedThisCity for the new city
                  );

                  continue;
                } else {
                  logger.info(
                    `🔁 Reached the end of all cities, starting over`
                  );
                  currentCityIndex = 0;

                  // Save progress with the reset city index
                  await updateProgressRecord(
                    0,
                    null,
                    {
                      completedbatchQueue: 0,
                      successfulbatchQueue: 0,
                      totalbatchQueue: 0,
                      totalJobsFound: 0,
                      totalJobsSaved: 0,
                      timestamp: new Date().toISOString(),
                      // occupationId removed
                    },
                    new Set<string>() // Reset completedThisCity for the new city
                  );
                }
              } else {
                logger.info(
                  `🔁 Resuming from occupation index ${startOccupationIndex} (using stored index)`
                );
              }
            } else {
              logger.info(
                `💾 Could not find occupation with ID ${lastOccupationIndex}, starting from the beginning of this city`
              );
            }
          }
          // Don't reset lastOccupationIndex to 0 - we need to keep it for the next iteration
          // Instead, store it in the metadata so we can resume from the correct point
          // We'll update it after processing batchQueue
        }

        // Log the current state of completedThisCity
        logger.info(
          `✅ Currently have ${completedThisCity.size} completed occupations for city ${city.name}`
        );

        // ——————————————————————————————————————————————
        // slice‑by‑index approach: workers pull the next chunk atomically
        // ——————————————————————————————————————————————
        // before spawning workers:
        const totalOcc = occupations.length;
        let nextIdx = startOccupationIndex;

        // launch one async worker loop per browser
        const workerPromises: Promise<void>[] = [];
        for (let workerId = 0; workerId < maxWorkers; workerId++) {
          workerPromises.push(
            (async () => {
              while (true) {
                // grab the next slice atomically
                const myStart = nextIdx;
                if (myStart >= totalOcc) break;
                nextIdx += MAX_OCCUPATIONS_PER_BATCH;

                // build our batch slice
                const slice = occupations
                  .slice(
                    myStart,
                    Math.min(myStart + MAX_OCCUPATIONS_PER_BATCH, totalOcc)
                  )
                  .filter((o) => !completedThisCity.has(String(o.id)));
                if (slice.length === 0) {
                  // nothing to do here, go grab the next slice
                  continue;
                }

                // create and process
                const batch: JobBatch = {
                  batchId: uuidv4(),
                  cityId: city.id,
                  cityName: city.name,
                  cityIndex: currentCityIndex,
                  stateId: city.state.id,
                  stateCode: city.state.code || "",
                  stateName: city.state.name,
                  occupations: slice.map((o) => ({ id: o.id, title: o.title })),
                  workerIndex: workerId,
                };

                logger.info(
                  `🚀 [Worker ${workerId}] Processing ${slice.length} occupations…`
                );
                const result = await processJobBatch(workerPool, batch);

                // mark completed IDs & update counters
                if (result.success && result.completedOccupationIds) {
                  for (const id of result.completedOccupationIds) {
                    completedThisCity.add(id);
                  }
                }

                // persist your progress immediately:
                await saveProgress(
                  /* lastOccIdx */ myStart + slice.length - 1,
                  /* cityIdx */ currentCityIndex,
                  /* metadata */ {
                    timestamp: new Date().toISOString(),
                    lastBatchId: batch.batchId,
                  },
                  /* completedThisCity */ completedThisCity
                );
              }

              logger.info(`👋 Worker ${workerId} done for city ${city.name}`);
            })()
          );
        }

        // wait until every slice is done
        await Promise.all(workerPromises);

        // at this point you know `completedThisCity` covers all occupations

        // Check if all occupations for this city have been processed
        const currentOccupationIds = occupations.map((o) => String(o.id));
        const allOccupationsCovered = currentOccupationIds.every((id) =>
          completedThisCity.has(id)
        );

        // Log the current coverage
        const completedCount = currentOccupationIds.filter((id) =>
          completedThisCity.has(id)
        ).length;
        logger.info(
          `✅ City ${city.name} coverage: ${completedCount}/${currentOccupationIds.length} occupations completed`
        );

        if (allOccupationsCovered) {
          logger.info(
            `✅ All occupations for city ${city.name} processed. Moving to next city.`
          );
          currentCityIndex++;

          // IMPORTANT: Set lastOccupationIndex to null, not 0
          // This ensures we start from the beginning of the next city
          // Setting to 0 could be confused with occupation ID 0
          lastOccupationIndex = null;

          logger.info(
            `💾 Moving to city index ${currentCityIndex} and resetting occupation index to null`
          );

          // Save progress with the updated city index
          await updateProgressRecord(
            currentCityIndex,
            null, // Use null instead of 0 for the occupation index
            {
              completedbatchQueue: 0,
              successfulbatchQueue: 0,
              totalbatchQueue: 0,
              totalJobsFound: 0,
              totalJobsSaved: 0,
              timestamp: new Date().toISOString(),
              // occupationId removed
            },
            new Set<string>() // Reset completedThisCity for the new city
          );
        } else {
          logger.info(
            `🔁 Not all occupations completed for ${city.name}, will resume remaining ones.`
          );
        }
      }

      logger.info(`📦 Created ${batchQueue.length} batchQueue of work`);

      // Mark batch creation as complete
      batchCreationComplete = true;

      await Promise.all(workerPromises);

      logger.info(`✅ All workers have completed processing`);

      // Calculate final statistics
      let completedbatchQueue = results.length;
      let successfulbatchQueue = results.filter((r) => r.success).length;
      let totalJobsFound = results.reduce(
        (sum, r) => sum + (r.jobsFound || 0),
        0
      );
      let totalJobsSaved = results.reduce(
        (sum, r) => sum + (r.jobsSaved || 0),
        0
      );

      // Calculate and log the total execution time
      const endTime = performance.now();
      const durationMs = endTime - startTime;
      const executionTimeMinutes = (durationMs / 1000 / 60).toFixed(2);

      // Log job statistics in standardized format for easier parsing
      logger.jobStats({
        jobType: "parallelJobScraper",
        processed: totalJobsFound,
        succeeded: totalJobsSaved,
        failed: totalJobsFound - totalJobsSaved,
        duration: durationMs,
        details: {
          batchesProcessed: completedbatchQueue,
          batchesSuccessful: successfulbatchQueue,
          batchesFailed: completedbatchQueue - successfulbatchQueue,
          successRate:
            totalJobsFound > 0
              ? ((totalJobsSaved / totalJobsFound) * 100).toFixed(1) + "%"
              : "N/A",
          executionTimeMinutes: executionTimeMinutes,
          memory: {
            rss: Math.round(process.memoryUsage().rss / 1024 / 1024),
            heapUsed: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
            heapTotal: Math.round(
              process.memoryUsage().heapTotal / 1024 / 1024
            ),
          },
        },
      });

      logger.info(
        `📊 Final results: ${completedbatchQueue} batches processed, ${successfulbatchQueue} successful, ${totalJobsFound} jobs found, ${totalJobsSaved} jobs saved`
      );
      logger.info(`⏱️ Total execution time: ${executionTimeMinutes} minutes`);

      // Function to process batches from the queue is now defined above

      // Function to process batches from the queue
    } catch (error) {
      logger.error(`❌ Error in runParallelJobScraper:`, error);

      // Log error with standardized job statistics
      const endTime = performance.now();
      const durationMs = endTime - startTime;

      logger.jobStats({
        jobType: "parallelJobScraper",
        processed: 0,
        succeeded: 0,
        failed: 1, // The job itself failed
        duration: durationMs,
        details: {
          error: error instanceof Error ? error.message : String(error),
          errorStack: error instanceof Error ? error.stack : undefined,
          memory: {
            rss: Math.round(process.memoryUsage().rss / 1024 / 1024),
            heapUsed: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
            heapTotal: Math.round(
              process.memoryUsage().heapTotal / 1024 / 1024
            ),
          },
        },
      });
    } finally {
      // Clean up memory monitoring
      if (memoryMonitorInterval) {
        clearInterval(memoryMonitorInterval);
        logger.info(`🧠 Memory monitoring stopped`);
      }

      // Stop the circuit breaker monitoring
      circuitBreaker.stopMonitoring();
      logger.info("🛑 Circuit breaker monitoring stopped");

      // Force garbage collection before exiting
      if (global.gc) {
        logger.info(`🧹 Running garbage collection before exit`);
        global.gc();
      }

      // Lock is now handled by the withJobLock utility in the main function

      // Clean up resources
      try {
        // Close Redis connection
        await redis.quit().catch((err) => {
          logger.error("Error closing Redis connection:", err);
        });

        // Disconnect Prisma
        await (await prisma).$disconnect().catch((err: any) => {
          logger.error("Error disconnecting Prisma:", err);
        });
      } catch (cleanupError) {
        logger.error("❌ Error during cleanup:", cleanupError);
      }
    }
  } catch (error) {
    logger.error(`❌ Error in runParallelJobScraper:`, error);

    // Record error in circuit breaker
    circuitBreaker.recordError();

    // Log error with standardized job statistics
    logger.jobStats({
      jobType: "parallelJobScraper",
      processed: 0,
      succeeded: 0,
      failed: 1, // The job itself failed
      duration: 0, // We don't have a startTime in this scope
      details: {
        error: error instanceof Error ? error.message : String(error),
        errorStack: error instanceof Error ? error.stack : undefined,
        memory: {
          rss: Math.round(process.memoryUsage().rss / 1024 / 1024),
          heapUsed: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
          heapTotal: Math.round(process.memoryUsage().heapTotal / 1024 / 1024),
        },
        circuitBreakerState: circuitBreaker.getState(),
      },
    });
  } finally {
    // Lock is now handled by the withJobLock utility in the main function

    // Clean up resources
    try {
      // Close Redis connection
      await redis.quit().catch((err) => {
        logger.error("Error closing Redis connection:", err);
      });

      // Disconnect Prisma
      await (await prisma).$disconnect().catch((err: Error) => {
        logger.error("Error disconnecting Prisma:", err);
      });
    } catch (cleanupError) {
      logger.error("❌ Error during cleanup:", cleanupError);
    }
  }
}

// When saving progress after processing batchQueue
async function saveProgress(
  lastOccupationIndex: number | null,
  cityIndex: number | null,
  metadata: any,
  completedThisCity: Set<string>
) {
  try {
    const progressTracker = new ProgressTracker("parallelJobScraper");

    // Get occupations to check if we need to increment city index
    const occupations = await (
      await prisma
    ).occupations.findMany({
      select: {
        id: true,
        title: true,
      },
      orderBy: {
        title: "asc",
      },
    });

    logger.info(`💾 DEBUG: Total occupations found: ${occupations.length}`);
    logger.info(
      `✅ City ${cityIndex} coverage: ${completedThisCity.size}/${occupations.length} occupations`
    );

    // Use the provided occupation index
    let validOccupationIndex: number | null = null;

    if (lastOccupationIndex === null) {
      validOccupationIndex = null;
      logger.info(
        `💾 Using null occupation index to start from the beginning of the city`
      );
    } else if (isNaN(Number(lastOccupationIndex))) {
      // If it's not a number, try to find the occupation by ID
      const occupationId = String(lastOccupationIndex);
      const idxById = occupations.findIndex(
        (o: { id: any }) => String(o.id) === occupationId
      );

      if (idxById >= 0) {
        validOccupationIndex = idxById;
        logger.info(
          `💾 Found occupation index ${validOccupationIndex} for ID ${occupationId}`
        );
      } else {
        // If we can't find the occupation by ID, use a small index to avoid getting stuck
        validOccupationIndex = 0;
        logger.warn(
          `⚠️ Could not find occupation with ID ${occupationId}, using index 0 to avoid getting stuck`
        );
      }
    } else {
      // It's a number, use it directly
      validOccupationIndex = Number(lastOccupationIndex);
      logger.info(
        `💾 Using provided occupation index: ${validOccupationIndex}`
      );
    }

    const validCityIndex = isNaN(Number(cityIndex)) ? null : Number(cityIndex);

    logger.info(
      `💾 Saving progress: occupation=${validOccupationIndex}, city=${validCityIndex}`
    );

    const occupationIdsForCity = occupations.map((o: { id: any }) =>
      String(o.id)
    );
    const isAllOccupationsCovered = occupationIdsForCity.every((id: string) =>
      completedThisCity.has(id)
    );

    let updatedCityIndex = validCityIndex;
    let updatedOccupationIndex: number | null = validOccupationIndex;

    if (isAllOccupationsCovered) {
      logger.info(
        `✅ All occupations completed for city index ${validCityIndex}, moving to next city...`
      );
      updatedCityIndex = validCityIndex !== null ? validCityIndex + 1 : 0;
      updatedOccupationIndex = null; // Use null instead of 0 to avoid confusion with occupation ID 0

      // 🔄 Ensure the set is fully populated before saving (in case we missed any in processing)
      completedThisCity.clear();
      occupationIdsForCity.forEach((id: string) => completedThisCity.add(id));
    }

    // Ensure metadata has required fields
    const validMetadata = {
      ...metadata,
      timestamp: metadata.timestamp || new Date().toISOString(),
      processedBy: "parallelJobScraper",
      jobsFound: metadata.jobsFound || 0,
      totalJobsFound: metadata.totalJobsFound || 0,
      // Store the occupation index to help with resuming
      occupationIndex: updatedOccupationIndex,
      // Store the completed occupation IDs
      completedOccupationIds: Array.from(completedThisCity),
    };

    const saved = await progressTracker.updateProgress({
      lastOccupationIndex: updatedOccupationIndex,
      lastCityIndex: updatedCityIndex,
      metadata: validMetadata,
    });

    if (saved) {
      // Verify the save was successful
      const verified = await progressTracker.verifyProgressSaved({
        lastOccupationIndex: updatedOccupationIndex,
        lastCityIndex: updatedCityIndex,
      });

      if (!verified) {
        logger.warn(
          `⚠️ Progress verification failed, attempting to update existing record directly`
        );

        try {
          // Try to find and update the existing record directly
          const existingRecord = await (
            await prisma
          ).scrapeProgress.findFirst({
            where: { type: "parallelJobScraper" },
            orderBy: { updatedAt: "desc" },
          });

          if (existingRecord) {
            await (
              await prisma
            ).scrapeProgress.update({
              where: { id: existingRecord.id },
              data: {
                lastOccupationIndex: updatedOccupationIndex,
                lastCityIndex: updatedCityIndex,
                metadata: JSON.stringify({
                  ...validMetadata,
                  recoveryAttempt: true,
                  recoveryTimestamp: new Date().toISOString(),
                }),
                updatedAt: new Date(),
              },
            });
            logger.info(
              `💾 Recovery update successful for record ID: ${existingRecord.id}`
            );
          } else {
            logger.warn(`⚠️ No existing record found for recovery update`);
          }
        } catch (recoveryError) {
          logger.error(`❌ Error during recovery update: ${recoveryError}`);
        }
      }
    } else {
      logger.error(`❌ Failed to save progress`);
    }
  } catch (error) {
    logger.error(`❌ Error in saveProgress:`, error);
  }
}

// Update the progress tracking to use lastOccupationIndex instead of lastOccupationId
async function updateProgressRecord(
  cityIndex: number,
  occupationIndex: string | number | null,
  metadata: {
    completedbatchQueue: number;
    successfulbatchQueue: number;
    totalbatchQueue: number;
    totalJobsFound: number;
    totalJobsSaved: number;
    timestamp: string;
    // occupationId removed from schema
  },
  completedThisCity: Set<string> = new Set<string>()
) {
  try {
    // Get occupations to check if we need to increment city index
    const occupations = await (
      await prisma
    ).occupations.findMany({
      select: {
        id: true,
        title: true,
      },
      orderBy: {
        title: "asc",
      },
    });

    // Determine the occupation index based on either the provided index or the ID in metadata
    let validOccupationIndex: number | null = null;

    if (occupationIndex === null) {
      // If null, keep it as null to start from the beginning of the city
      validOccupationIndex = null;
      logger.info(
        `💾 No occupation index provided, keeping as null to start from the beginning of the city`
      );
    } else if (isNaN(Number(occupationIndex))) {
      // If it's not a number, try to find the occupation by ID
      const occupationId = String(occupationIndex);
      const idxById = occupations.findIndex(
        (o: { id: any }) => String(o.id) === occupationId
      );

      if (idxById >= 0) {
        validOccupationIndex = idxById;
        logger.info(
          `💾 Found occupation index ${validOccupationIndex} for ID ${occupationId}`
        );
      } else {
        validOccupationIndex = null;
        logger.warn(
          `⚠️ Could not find occupation with ID ${occupationId}, starting from the beginning of the city`
        );
      }
    } else {
      // It's a number, use it directly
      validOccupationIndex = Number(occupationIndex);
      logger.info(
        `💾 Using provided occupation index: ${validOccupationIndex}`
      );
    }

    const validCityIndex = isNaN(Number(cityIndex)) ? null : Number(cityIndex);

    // Check if we've reached the end of occupations for this city
    let updatedCityIndex = validCityIndex;
    let updatedOccupationIndex = validOccupationIndex;

    // If we have a valid occupation index and it's the last one in the list
    if (validOccupationIndex !== null && occupations.length > 0) {
      // If we're at the last occupation, we should move to the next city
      if (validOccupationIndex >= occupations.length - 1) {
        logger.info(
          `🔄 Reached the end of occupations for city ${validCityIndex}, moving to next city`
        );
        updatedCityIndex = validCityIndex !== null ? validCityIndex + 1 : 0;
        updatedOccupationIndex = null; // Reset occupation index to null for the new city
      }
    }

    // Log the completed occupations for this city
    logger.info(
      `✅ City ${validCityIndex} coverage: ${completedThisCity.size} occupations completed`
    );

    // Check if all occupations are completed for this city
    const occupationIdsForCity = occupations.map((o: { id: any }) =>
      String(o.id)
    );
    const isAllOccupationsCovered = occupationIdsForCity.every((id: string) =>
      completedThisCity.has(id)
    );

    if (isAllOccupationsCovered) {
      logger.info(
        `✅ All occupations completed for city index ${validCityIndex}, moving to next city...`
      );
      updatedCityIndex = validCityIndex !== null ? validCityIndex + 1 : 0;
      updatedOccupationIndex = null; // Use null instead of 0 to avoid confusion with occupation ID 0
    }

    // Add completedThisCity to metadata
    const updatedMetadata = {
      ...metadata,
      completedOccupationIds: Array.from(completedThisCity),
    };

    // First check if a parallelJobScraper record exists
    const existingRecord = await (
      await prisma
    ).scrapeProgress.findFirst({
      where: { type: "parallelJobScraper" },
      orderBy: { updatedAt: "desc" },
    });

    if (existingRecord) {
      logger.info(
        `💾 Updating progress: City index ${validCityIndex} -> ${updatedCityIndex}, Occupation index ${validOccupationIndex} -> ${updatedOccupationIndex}`
      );

      // Update the existing record
      await (
        await prisma
      ).scrapeProgress.update({
        where: { id: existingRecord.id },
        data: {
          lastCityIndex: updatedCityIndex,
          lastOccupationIndex: updatedOccupationIndex,
          metadata: JSON.stringify(updatedMetadata),
          updatedAt: new Date(),
        },
      });
    } else {
      // Check if any record with this type exists (might have been created by another process)
      const anyExistingRecord = await (
        await prisma
      ).scrapeProgress.findFirst({
        where: { type: "parallelJobScraper" },
      });

      if (anyExistingRecord) {
        // If a record exists, update it instead of creating a new one
        logger.info(
          `💾 Found existing record with type parallelJobScraper, updating instead of creating new one`
        );

        await (
          await prisma
        ).scrapeProgress.update({
          where: { id: anyExistingRecord.id },
          data: {
            lastCityIndex: updatedCityIndex,
            lastOccupationIndex: updatedOccupationIndex,
            metadata: JSON.stringify(updatedMetadata),
            updatedAt: new Date(),
          },
        });
      } else {
        // Create a new record only if no record with this type exists
        logger.info(
          `💾 Creating new progress record: City index ${validCityIndex} -> ${updatedCityIndex}, Occupation index ${validOccupationIndex} -> ${updatedOccupationIndex}`
        );

        await (
          await prisma
        ).scrapeProgress.create({
          data: {
            id: `progress_${Date.now()}`,
            type: "parallelJobScraper",
            lastCityIndex: updatedCityIndex,
            lastOccupationIndex: updatedOccupationIndex,
            metadata: JSON.stringify(updatedMetadata),
          },
        });
      }
    }
  } catch (error) {
    logger.error(`❌ Error updating progress record: ${error}`);
  }
}
