// scraper/jobs/parallelJobScraper.ts
// Simplified parallel job scraper

import { logger } from "../utils/logger";
import {
  sendEmailNotification,
  EmailNotificationType,
} from "../utils/emailService";
import { getPrismaClient } from "../utils/prismaClient";
import { config } from "../config";
import Redis from "ioredis";
import { CircuitState } from "../utils/improvedImprovedCircuitBreaker";
import os from "os";

// Initialize Prisma client variable
let prisma: any;

// Create Redis client for circuit breaker
const redis = new Redis(config.redis.url as string);

/**
 * Get the current circuit breaker state from Redis
 */
async function getCircuitBreakerState(): Promise<CircuitState> {
  try {
    const state = await redis.get("circuit_breaker:state");
    return (state as CircuitState) || CircuitState.CLOSED;
  } catch (error) {
    logger.error(
      `❌ Error getting circuit breaker state: ${
        error instanceof Error ? error.message : String(error)
      }`
    );
    return CircuitState.CLOSED; // Default to CLOSED if we can't get the state
  }
}

/**
 * Simplified parallel job scraper - just logs a message for now
 */
async function runParallelJobScraper() {
  // Initialize Prisma client
  prisma = await getPrismaClient("web");
  logger.info("✅ Prisma client initialized");

  // Check system resources
  const totalMemory = os.totalmem();
  const freeMemory = os.freemem();
  const usedMemory = totalMemory - freeMemory;
  const memoryUsage = (usedMemory / totalMemory) * 100;

  logger.info(`🧠 System resources - Memory: ${memoryUsage.toFixed(2)}%`);

  // If resources are critically constrained, exit early
  if (memoryUsage > 90) {
    logger.error(`❌ Critical memory usage: ${memoryUsage.toFixed(2)}%`);

    await sendEmailNotification(EmailNotificationType.SYSTEM_OVERLOAD, {
      timestamp: new Date().toISOString(),
      message: "Job scraper aborted due to critical memory usage",
      memoryUsage: `${memoryUsage.toFixed(2)}%`,
    }).catch((err) =>
      logger.error(`Failed to send resource constraint email: ${err}`)
    );

    throw new Error("Critical memory usage - aborting job scraper");
  }

  logger.info("🚀 Parallel job scraper running...");

  // For now, just simulate some work
  logger.info("⏳ Simulating job scraping work...");
  await new Promise((resolve) => setTimeout(resolve, 5000));

  logger.info("✅ Parallel job scraper completed successfully");
}

/**
 * Main function to run the job with proper cleanup
 */
async function main() {
  try {
    // Check circuit breaker state first
    const circuitBreakerState = await getCircuitBreakerState();

    if (circuitBreakerState === CircuitState.OPEN) {
      logger.error("❌ Circuit breaker is OPEN, skipping job execution");
      return 1;
    }

    logger.info("🚀 Starting parallel job scraper...");

    // Check if we're in test mode
    const isTestMode =
      process.env.TEST_MODE === "true" || process.argv.includes("--test-mode");

    if (isTestMode) {
      logger.info(
        "🧪 Running in test mode - simulating work without Redis locking"
      );

      // Simulate some work without Redis locking
      logger.info("⏳ Simulating work for 5 seconds...");
      await new Promise((resolve) => setTimeout(resolve, 5000));

      logger.info("✅ Test completed successfully");
    } else {
      // Normal operation - run the simplified scraper
      logger.info("🚀 Running simplified parallel job scraper");
      await runParallelJobScraper();
    }

    logger.info("✅ Parallel job scraper completed successfully");
    return 0;
  } catch (error) {
    logger.error("❌ Parallel job scraper failed:", error);
    return 1;
  }
}

// Run the job if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  logger.info("Running parallelJobScraper directly...");
  main()
    .then((exitCode) => {
      process.exit(exitCode);
    })
    .catch(async (error) => {
      logger.error("Unhandled error in main:", error);
      process.exit(1);
    });
}

export { runParallelJobScraper };
