// scraper/jobs/parallelJobScraper.ts
// Parallel job scraper with WorkerPool

import { logger } from "../utils/logger";
import {
  sendEmailNotification,
  EmailNotificationType,
} from "../utils/emailService";
import { getPrismaClient } from "../utils/prismaClient";
import { config } from "../config";
import Redis from "ioredis";
import { CircuitState } from "../utils/improvedImprovedCircuitBreaker";
import { getSharedCircuitBreaker } from "../utils/sharedCircuitBreaker";
import { WorkerPool } from "../workers/workerPool";
import { processJobBatch } from "../workers/jobScraperWorker";
import { JobBatch } from "../utils/types";
import { v4 as uuidv4 } from "uuid";
import os from "os";

// Initialize Prisma client variable
let prisma: any;

// Create Redis client for circuit breaker
const redis = new Redis(config.redis.url as string);

/**
 * Get the current circuit breaker state from Redis
 */
async function getCircuitBreakerState(): Promise<CircuitState> {
  try {
    const state = await redis.get("circuit_breaker:state");
    return (state as CircuitState) || CircuitState.CLOSED;
  } catch (error) {
    logger.error(
      `❌ Error getting circuit breaker state: ${
        error instanceof Error ? error.message : String(error)
      }`
    );
    return CircuitState.CLOSED; // Default to CLOSED if we can't get the state
  }
}

/**
 * Parallel job scraper with WorkerPool
 */
async function runParallelJobScraper() {
  // Initialize Prisma client
  prisma = await getPrismaClient("web");
  logger.info("✅ Prisma client initialized");

  // Check system resources
  const totalMemory = os.totalmem();
  const freeMemory = os.freemem();
  const usedMemory = totalMemory - freeMemory;
  const memoryUsage = (usedMemory / totalMemory) * 100;

  logger.info(`🧠 System resources - Memory: ${memoryUsage.toFixed(2)}%`);

  // If resources are critically constrained, exit early
  if (memoryUsage > 90) {
    logger.error(`❌ Critical memory usage: ${memoryUsage.toFixed(2)}%`);

    await sendEmailNotification(EmailNotificationType.SYSTEM_OVERLOAD, {
      timestamp: new Date().toISOString(),
      message: "Job scraper aborted due to critical memory usage",
      memoryUsage: `${memoryUsage.toFixed(2)}%`,
    }).catch((err) =>
      logger.error(`Failed to send resource constraint email: ${err}`)
    );

    throw new Error("Critical memory usage - aborting job scraper");
  }

  logger.info("🚀 Starting parallel job scraper with WorkerPool...");

  // Initialize WorkerPool
  const workerPool = new WorkerPool({
    maxWorkers: Math.min(3, os.cpus().length), // Limit to 3 workers or CPU count
    headless: true,
    slowMo: 20,
    recycleThreshold: 5,
  });

  try {
    // Initialize the worker pool
    await workerPool.initialize();
    logger.info(
      `✅ WorkerPool initialized with ${workerPool.getTotalWorkerCount()} workers`
    );

    // Create some sample batches to process
    const sampleBatches: JobBatch[] = [
      {
        batchId: uuidv4(),
        cityId: "1",
        cityName: "San Francisco",
        cityIndex: 0,
        stateId: "1",
        stateCode: "CA",
        stateName: "California",
        occupations: [
          { id: "1", title: "Software Engineer" },
          { id: "2", title: "Data Scientist" },
        ],
        workerIndex: 0,
      },
      {
        batchId: uuidv4(),
        cityId: "2",
        cityName: "Los Angeles",
        cityIndex: 1,
        stateId: "1",
        stateCode: "CA",
        stateName: "California",
        occupations: [
          { id: "3", title: "Product Manager" },
          { id: "4", title: "UX Designer" },
        ],
        workerIndex: 1,
      },
    ];

    // Process batches
    const results = [];
    for (const batch of sampleBatches) {
      logger.info(`🔄 Processing batch ${batch.batchId}...`);
      const result = await processJobBatch(workerPool, batch);
      results.push(result);
      logger.info(
        `✅ Batch ${batch.batchId} completed: ${result.success ? "Success" : "Failed"}`
      );
    }

    // Log summary
    const totalJobsFound = results.reduce(
      (sum, r) => sum + (r.jobsFound || 0),
      0
    );
    const totalJobsSaved = results.reduce(
      (sum, r) => sum + (r.jobsSaved || 0),
      0
    );
    const successfulBatches = results.filter((r) => r.success).length;

    logger.info(
      `📊 Summary: ${successfulBatches}/${results.length} batches successful, ${totalJobsFound} jobs found, ${totalJobsSaved} jobs saved`
    );
  } finally {
    // Clean up WorkerPool
    await workerPool.shutdown();
    logger.info("✅ WorkerPool shut down successfully");
  }

  logger.info("✅ Parallel job scraper completed successfully");
}

/**
 * Main function to run the job with proper cleanup
 */
async function main() {
  try {
    // Check circuit breaker state first
    const circuitBreakerState = await getCircuitBreakerState();

    if (circuitBreakerState === CircuitState.OPEN) {
      logger.error("❌ Circuit breaker is OPEN, skipping job execution");
      return 1;
    }

    logger.info("🚀 Starting parallel job scraper...");

    // Check if we're in test mode
    const isTestMode =
      process.env.TEST_MODE === "true" || process.argv.includes("--test-mode");

    if (isTestMode) {
      logger.info(
        "🧪 Running in test mode - simulating work without Redis locking"
      );

      // Simulate some work without Redis locking
      logger.info("⏳ Simulating work for 5 seconds...");
      await new Promise((resolve) => setTimeout(resolve, 5000));

      logger.info("✅ Test completed successfully");
    } else {
      // Normal operation - run the simplified scraper
      logger.info("🚀 Running simplified parallel job scraper");
      await runParallelJobScraper();
    }

    logger.info("✅ Parallel job scraper completed successfully");
    return 0;
  } catch (error) {
    logger.error("❌ Parallel job scraper failed:", error);
    return 1;
  }
}

// Run the job if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  logger.info("Running parallelJobScraper directly...");
  main()
    .then((exitCode) => {
      process.exit(exitCode);
    })
    .catch(async (error) => {
      logger.error("Unhandled error in main:", error);
      process.exit(1);
    });
}

export { runParallelJobScraper };
