"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.JobService = void 0;
const client_1 = require("@prisma/client");
const logger_1 = require("../utils/logger");
const matchOrCreateCompany_1 = require("../lib/matchOrCreateCompany");
const cleanJobData_1 = require("../lib/cleanJobData");
const extractRemoteType_1 = require("../lib/extractRemoteType");
const prisma = new client_1.PrismaClient();
class JobService {
    /**
     * Cleans and standardizes job data
     */
    async cleanJobData(job) {
        // Clean the basic fields
        const cleanedTitle = (0, cleanJobData_1.cleanJobTitle)(job.title);
        const cleanedCompany = (0, cleanJobData_1.cleanCompanyName)(job.company);
        // Special handling for LinkedIn indicators in location
        let cleanedLocation = (0, cleanJobData_1.cleanLocation)(job.location);
        // If location is "Unknown" and we have a company name, try to determine location from company
        if (cleanedLocation === "Unknown" && job.company) {
            // This is a mapping of companies to their known headquarters locations
            const companyLocations = {
                // Agoura Hills companies
                Pennymac: "Agoura Hills, CA",
                "The Guitar Center": "Agoura Hills, CA",
                "Guitar Center": "Agoura Hills, CA",
                "The Workshop La": "Agoura Hills, CA",
                "Publicis Groupe": "Agoura Hills, CA",
                // Los Angeles companies
                "Regime Music Group": "Los Angeles, CA",
                "Dream House Cinema": "Los Angeles, CA",
                // Other California companies
                "Joni and Friends": "Agoura Hills, CA",
                "Albert & Mackenzie": "Los Angeles, CA",
                "Black Box Security": "Los Angeles, CA",
            };
            // Use the company's location if we know it
            if (companyLocations[cleanedCompany]) {
                cleanedLocation = companyLocations[cleanedCompany];
            }
            else if (job.stateId) {
                // If we don't know the company's location but we have a state, use the state
                const state = await prisma.state.findUnique({
                    where: { id: job.stateId },
                });
                if (state) {
                    cleanedLocation = state.code;
                }
            }
        }
        // Extract remote type from location and description
        const remoteType = (0, extractRemoteType_1.extractRemoteType)(job.location, job.description);
        const cleanedJob = {
            ...job,
            title: cleanedTitle,
            company: cleanedCompany,
            location: cleanedLocation,
            remoteType: remoteType || job.remoteType || "",
        };
        // We're no longer cleaning the description here
        // This allows us to preserve the HTML content for later processing
        // The description will be cleaned when it's displayed to the user
        // or when it's processed for specific purposes
        return cleanedJob;
    }
    /**
     * Cleans job description by removing redundant title/location/ID information
     * and standardizing formatting
     */
    cleanJobDescription(description, cleanedTitle) {
        if (!description)
            return "";
        let cleaned = description;
        // Remove "Job description" prefix from the beginning (more aggressive approach)
        cleaned = cleaned.replace(/^\s*job\s+description\s*:?\s*/i, "");
        // Also try with different spacing and punctuation
        cleaned = cleaned.replace(/^\s*job-description\s*:?\s*/i, "");
        cleaned = cleaned.replace(/^\s*job_description\s*:?\s*/i, "");
        // Remove job title with location and ID pattern from the beginning
        // Pattern like: "Job title Alhambra, CA, Job ID 76467"
        cleaned = cleaned.replace(/^(.+?)\s*,\s*[A-Z]{2}\s*,\s*Job\s*ID\s*\d+/i, "");
        // Remove the job title if it appears at the beginning of the description
        if (cleanedTitle && cleaned.startsWith(cleanedTitle)) {
            cleaned = cleaned.substring(cleanedTitle.length).trim();
            // Also remove any punctuation that might follow the title
            cleaned = cleaned.replace(/^[:\-,;\s]+/, "");
        }
        // Also try to match the pattern without "Job description" prefix
        const titleLocationIdPattern = new RegExp(`^(${cleanedTitle}\\s+.+?)\\s*,\\s*[A-Z]{2}\\s*,\\s*Job\\s*ID\\s*\\d+`, "i");
        cleaned = cleaned.replace(titleLocationIdPattern, "");
        // Remove any URLs at the end of the description
        cleaned = cleaned.replace(/To apply, please visit: .+$/i, "");
        cleaned = cleaned.replace(/Apply now at: .+$/i, "");
        cleaned = cleaned.replace(/Apply online at: .+$/i, "");
        cleaned = cleaned.replace(/Apply at: .+$/i, "");
        // Remove any HTML entities
        cleaned = cleaned
            .replace(/&amp;/g, "&")
            .replace(/&lt;/g, "<")
            .replace(/&gt;/g, ">")
            .replace(/&quot;/g, '"')
            .replace(/&#39;/g, "'");
        // Identify common section headers and add line breaks
        const sectionHeaders = [
            // Remove "Job description" from the list to avoid adding it back
            // "Job description",
            "About the role",
            "About the job",
            "About the position",
            "Responsibilities",
            "Key responsibilities",
            "Your responsibilities",
            "What you'll do",
            "What you will do",
            "Requirements",
            "Qualifications",
            "Required qualifications",
            "Required skills",
            "What you'll need",
            "What you will need",
            "Benefits",
            "What we offer",
            "Compensation",
            "Compensation and benefits",
            "About us",
            "About the company",
            "Who we are",
            "About the team",
        ];
        // Create a regex pattern for section headers
        const headerPattern = new RegExp(`(${sectionHeaders.join("|")})\\s*:?\\s*`, "gi");
        // Add line breaks before section headers
        cleaned = cleaned.replace(headerPattern, "\n\n$1: ");
        // Also remove "Job description:" if it appears in the middle of the text (more aggressive approach)
        cleaned = cleaned.replace(/\s*job\s+description\s*:?\s*/gi, " ");
        cleaned = cleaned.replace(/\s*job-description\s*:?\s*/gi, " ");
        cleaned = cleaned.replace(/\s*job_description\s*:?\s*/gi, " ");
        // Check if we still have "Job description" in the text
        if (cleaned.match(/job\s+description/i)) {
            console.log(`WARNING: 'Job description' still found in the text after cleaning`);
        }
        // Identify bullet points and add line breaks
        cleaned = cleaned.replace(/([.!?])\s+([A-Z])/g, "$1\n$2");
        // Add line breaks for bullet points that might be in the text
        cleaned = cleaned.replace(/([.!?])\s*•\s*/g, "$1\n• ");
        cleaned = cleaned.replace(/([.!?])\s*-\s*/g, "$1\n- ");
        cleaned = cleaned.replace(/([.!?])\s*\*\s*/g, "$1\n* ");
        cleaned = cleaned.replace(/([.!?])\s*(\d+)\.\s*/g, "$1\n$2. ");
        // Also handle bullet points at the beginning of the text
        cleaned = cleaned.replace(/^\s*•\s*/g, "• ");
        cleaned = cleaned.replace(/^\s*-\s*/g, "- ");
        cleaned = cleaned.replace(/^\s*\*\s*/g, "* ");
        cleaned = cleaned.replace(/^\s*(\d+)\.\s*/g, "$1. ");
        // Standardize whitespace but preserve line breaks
        // First, replace newlines with a special marker
        cleaned = cleaned.replace(/\n/g, "__NEWLINE__");
        // Then standardize whitespace
        cleaned = cleaned.replace(/\s+/g, " ").trim();
        // Finally, restore newlines
        cleaned = cleaned.replace(/__NEWLINE__/g, "\n");
        // Replace double line breaks with single line breaks
        cleaned = cleaned.replace(/\n\n/g, "\n");
        console.log(`Cleaned description length: ${cleaned.length} characters`);
        return cleaned;
    }
    /**
     * Saves a batch of jobs to the database
     */
    async saveJobs(jobs) {
        let saved = 0;
        let skipped = 0;
        const usStates = await prisma.state.findMany({
            where: { country: { isoCode: "US" } },
            select: { id: true, code: true, name: true },
        });
        for (const job of jobs) {
            try {
                // Clean the job data before saving
                const cleanedJob = this.cleanJobData(job);
                // Check if job already exists by URL
                const existingJob = await prisma.jobListing.findFirst({
                    where: {
                        url: cleanedJob.url,
                    },
                });
                if (existingJob) {
                    logger_1.logger.info(`⏭️ Skipped: ${cleanedJob.title} at ${cleanedJob.company} (already exists)`);
                    skipped++;
                    continue;
                }
                // Parse the postedDate if it's a string
                let parsedPostedDate = null;
                if (cleanedJob.postedDate &&
                    typeof cleanedJob.postedDate === "string") {
                    parsedPostedDate = this.parsePostedDate(cleanedJob.postedDate);
                }
                // determine stateId: use provided, else parse from location, else fallback to CA
                let stateId = cleanedJob.stateId;
                // 1) if no explicit stateId, try to extract two‑letter code from the location string
                if (!stateId) {
                    const m = cleanedJob.location.match(/,\s*([A-Z]{2})\b/);
                    if (m) {
                        const st = usStates.find((s) => s.code === m[1]);
                        if (st) {
                            stateId = st.id;
                            logger_1.logger.info(`🌎 Parsed state ${m[1]} for job: ${cleanedJob.title}`);
                        }
                    }
                }
                // 2) still no match? try matching by full state name
                if (!stateId) {
                    const st = usStates.find((s) => cleanedJob.location.toLowerCase().includes(s.name.toLowerCase()));
                    if (st) {
                        stateId = st.id;
                        logger_1.logger.info(`🌎 Matched state name ${st.name} for job: ${cleanedJob.title}`);
                    }
                }
                // 3) last resort, fallback to California
                if (!stateId) {
                    const ca = usStates.find((s) => s.code === "CA");
                    if (ca) {
                        stateId = ca.id;
                        logger_1.logger.warn(`🚨 No state parsed—defaulting to California for job: ${cleanedJob.title}`);
                    }
                    else {
                        throw new Error("Could not find any fallback state");
                    }
                }
                // Match or create the company
                const companyId = await (0, matchOrCreateCompany_1.matchOrCreateCompany)(cleanedJob.company, {
                    description: cleanedJob.description,
                    applyLink: cleanedJob.url,
                }, stateId); // Pass stateId to associate with company
                // Log company matching result
                if (companyId) {
                    logger_1.logger.info(`🏢 Matched/created company for job: ${cleanedJob.company} (ID: ${companyId})`);
                }
                else {
                    logger_1.logger.warn(`⚠️ Could not match/create company for job: ${cleanedJob.company}`);
                }
                // Save the job with company and state associations
                await prisma.jobListing.create({
                    data: {
                        jobId: `${cleanedJob.platform || "bing"}_${Date.now()}_${Math.random().toString(36).substring(2, 10)}`,
                        title: cleanedJob.title,
                        company: cleanedJob.company,
                        location: cleanedJob.location,
                        url: cleanedJob.url,
                        platform: cleanedJob.platform || cleanedJob.source, // Use platform or source
                        postedDate: parsedPostedDate, // Use the parsed date
                        createdAt: cleanedJob.dateFound || new Date().toISOString(),
                        lastCheckedAt: new Date(),
                        // Add company association if we have a company ID
                        ...(companyId ? { companyId } : {}),
                        // Always include stateId - it should be guaranteed to exist by this point
                        stateId,
                    },
                });
                saved++;
                logger_1.logger.info(`✅ Saved: ${cleanedJob.title} at ${cleanedJob.company}`);
            }
            catch (error) {
                logger_1.logger.error(`❌ Error saving job ${job.title} at ${job.company}: ${error}`);
            }
        }
        if (saved > 0) {
            logger_1.logger.info(`💾 Saved ${saved} new jobs to database`);
        }
        if (skipped > 0) {
            logger_1.logger.info(`⏭️ Skipped ${skipped} existing jobs`);
        }
        if (saved === 0 && skipped === 0) {
            logger_1.logger.info(`ℹ️ No jobs to save`);
        }
        return { saved, skipped };
    }
    /**
     * Saves a single job with company matching/creation
     */
    async saveJobWithCompany(job) {
        try {
            // Clean the job data before saving
            const cleanedJob = this.cleanJobData(job);
            // Match or create the company
            const company = await (0, matchOrCreateCompany_1.matchOrCreateCompany)(cleanedJob.company);
            // Guard against undefined company
            if (!company) {
                logger_1.logger.error(`Failed to create or find company: ${cleanedJob.company}`);
                return null;
            }
            // Check if job already exists by URL (which has a unique constraint)
            const existingJobByUrl = await prisma.jobListing.findUnique({
                where: { url: cleanedJob.url || "" },
            });
            if (existingJobByUrl) {
                logger_1.logger.info(`📋 Job with URL already exists: ${cleanedJob.title} at ${cleanedJob.company}`);
                return existingJobByUrl;
            }
            // Also check by title, company, and location using our fingerprint approach
            const normalizedTitle = cleanedJob.title?.toLowerCase().trim() || "";
            const normalizedCompany = cleanedJob.company?.toLowerCase().trim() || "";
            const normalizedLocation = cleanedJob.location?.toLowerCase().trim() || "";
            const existingJob = await prisma.jobListing.findFirst({
                where: {
                    title: { equals: normalizedTitle, mode: "insensitive" },
                    company: { equals: normalizedCompany, mode: "insensitive" },
                    location: { equals: normalizedLocation, mode: "insensitive" },
                },
            });
            if (existingJob) {
                logger_1.logger.info(`📋 Job already exists: ${cleanedJob.title} at ${cleanedJob.company}`);
                return existingJob;
            }
            // Generate a unique jobId if not provided
            const jobId = job.jobId ||
                `${job.platform || "bing"}_${Date.now()}_${Math.random().toString(36).substring(2, 10)}`;
            // Parse the postedDate if it's a string
            let parsedPostedDate = null;
            if (job.postedDate && typeof job.postedDate === "string") {
                parsedPostedDate = this.parsePostedDate(job.postedDate);
            }
            // Ensure we have a valid stateId
            let stateId = job.stateId;
            if (!stateId) {
                // Try to extract from location
                const stateCodeMatch = job.location?.match(/,\s*([A-Z]{2})\b/);
                if (stateCodeMatch && stateCodeMatch[1]) {
                    const stateCode = stateCodeMatch[1];
                    const state = await prisma.state.findFirst({
                        where: {
                            code: stateCode,
                            country: {
                                isoCode: "US",
                            },
                        },
                        select: { id: true },
                    });
                    if (state) {
                        stateId = state.id;
                        logger_1.logger.info(`🌎 Found state for job: ${stateCode} (ID: ${stateId})`);
                    }
                }
                // If still no stateId, use default (California)
                if (!stateId) {
                    const defaultState = await prisma.state.findFirst({
                        where: {
                            name: "California",
                            country: {
                                isoCode: "US",
                            },
                        },
                        select: { id: true },
                    });
                    if (defaultState) {
                        stateId = defaultState.id;
                        logger_1.logger.info(`🚨 Using default state (California) for job: ${job.title}`);
                    }
                    else {
                        logger_1.logger.error(`❌ Critical error: Could not find default state (California) in database`);
                        throw new Error("Default state not found in database");
                    }
                }
            }
            // Create the job
            const newJob = await prisma.jobListing.create({
                data: {
                    jobId,
                    title: job.title,
                    description: job.description || "",
                    location: job.location || "",
                    url: job.url || "",
                    platform: job.platform || "bing",
                    companyId: company,
                    stateId, // Now guaranteed to be a valid stateId
                    company: job.company,
                    postedDate: parsedPostedDate, // Use the parsed date
                    lastCheckedAt: new Date(),
                },
            });
            logger_1.logger.info(`📋 Created new job: ${job.title} at ${job.company}`);
            return newJob;
        }
        catch (error) {
            logger_1.logger.error(`Error saving job "${job.title}" at "${job.company}": ${error}`);
            return null;
        }
    }
    /**
     * Generates a unique key for a job to avoid duplicates
     */
    generateJobKey(job) {
        // Normalize strings for consistent comparison
        const normalizedTitle = job.title?.toLowerCase().trim() || "";
        const normalizedCompany = job.company?.toLowerCase().trim() || "";
        const normalizedLocation = job.location?.toLowerCase().trim() || "";
        // Create a consistent fingerprint format matching saveJobsToDatabase.ts
        return `${normalizedCompany}|${normalizedTitle}|${normalizedLocation}`;
    }
    /**
     * Deduplicates an array of jobs using the generateJobKey method
     */
    deduplicateJobs(jobs) {
        const seen = new Set();
        const uniqueJobs = [];
        for (const job of jobs) {
            const key = this.generateJobKey(job);
            if (!seen.has(key)) {
                seen.add(key);
                uniqueJobs.push(job);
            }
        }
        return uniqueJobs;
    }
    // Parse relative dates into ISO-8601 format
    parsePostedDate(relativeDate) {
        try {
            if (!relativeDate)
                return null;
            // Normalize the input
            const normalizedDate = relativeDate.toLowerCase().trim();
            const now = new Date();
            // Handle "just now", "today", etc.
            if (normalizedDate === "just now" || normalizedDate === "today") {
                return now;
            }
            // Handle "yesterday"
            if (normalizedDate === "yesterday") {
                const date = new Date(now);
                date.setDate(date.getDate() - 1);
                return date;
            }
            // Handle "X days ago" format
            if (normalizedDate.includes("day")) {
                const match = normalizedDate.match(/(\d+)\s*day/);
                if (match && match[1]) {
                    const days = parseInt(match[1]);
                    const date = new Date(now);
                    date.setDate(date.getDate() - days);
                    return date;
                }
            }
            // Handle "X hours ago" format
            if (normalizedDate.includes("hour")) {
                const match = normalizedDate.match(/(\d+)\s*hour/);
                if (match && match[1]) {
                    const hours = parseInt(match[1]);
                    const date = new Date(now);
                    date.setHours(date.getHours() - hours);
                    return date;
                }
            }
            // Handle "X weeks ago" format
            if (normalizedDate.includes("week")) {
                const match = normalizedDate.match(/(\d+)\s*week/);
                if (match && match[1]) {
                    const weeks = parseInt(match[1]);
                    const date = new Date(now);
                    date.setDate(date.getDate() - weeks * 7);
                    return date;
                }
            }
            // Handle "X months ago" format
            if (normalizedDate.includes("month")) {
                const match = normalizedDate.match(/(\d+)\s*month/);
                if (match && match[1]) {
                    const months = parseInt(match[1]);
                    const date = new Date(now);
                    date.setMonth(date.getMonth() - months);
                    return date;
                }
            }
            // Handle month and day format (e.g., "January 21")
            const monthNames = [
                "january",
                "february",
                "march",
                "april",
                "may",
                "june",
                "july",
                "august",
                "september",
                "october",
                "november",
                "december",
            ];
            const shortMonthNames = [
                "jan",
                "feb",
                "mar",
                "apr",
                "may",
                "jun",
                "jul",
                "aug",
                "sep",
                "oct",
                "nov",
                "dec",
            ];
            // Check for full month names
            for (let i = 0; i < monthNames.length; i++) {
                if (normalizedDate.includes(monthNames[i])) {
                    const dayMatch = normalizedDate.match(/(\d+)/);
                    if (dayMatch && dayMatch[1]) {
                        const day = parseInt(dayMatch[1]);
                        const date = new Date(now.getFullYear(), i, day);
                        // If the resulting date is in the future, it's probably from last year
                        if (date > now) {
                            date.setFullYear(date.getFullYear() - 1);
                        }
                        return date;
                    }
                }
            }
            // Check for abbreviated month names
            for (let i = 0; i < shortMonthNames.length; i++) {
                if (normalizedDate.includes(shortMonthNames[i])) {
                    const dayMatch = normalizedDate.match(/(\d+)/);
                    if (dayMatch && dayMatch[1]) {
                        const day = parseInt(dayMatch[1]);
                        const date = new Date(now.getFullYear(), i, day);
                        // If the resulting date is in the future, it's probably from last year
                        if (date > now) {
                            date.setFullYear(date.getFullYear() - 1);
                        }
                        return date;
                    }
                }
            }
            // Try to parse as ISO date directly
            if (normalizedDate.match(/\d{4}-\d{2}-\d{2}/)) {
                const date = new Date(normalizedDate);
                if (!isNaN(date.getTime())) {
                    return date;
                }
            }
            // If we can't parse it, return null
            logger_1.logger.warn(`Could not parse date format: ${relativeDate}`);
            return null;
        }
        catch (error) {
            logger_1.logger.error(`Error parsing date: ${relativeDate}`, error);
            return null;
        }
    }
}
exports.JobService = JobService;
