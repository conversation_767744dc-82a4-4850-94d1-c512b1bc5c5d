"use strict";
// cron/utils/sharedCircuitBreaker.ts
// Shared circuit breaker instance for all jobs to use
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getSharedCircuitBreaker = getSharedCircuitBreaker;
exports.resetSharedCircuitBreaker = resetSharedCircuitBreaker;
exports.withCircuitBreaker = withCircuitBreaker;
const logger_1 = require("./logger");
const improvedImprovedCircuitBreaker_1 = require("./improvedImprovedCircuitBreaker");
const config_1 = require("../config");
const emailService_1 = require("./emailService");
const os_1 = __importDefault(require("os"));
// Create a singleton instance of the circuit breaker
let circuitBreakerInstance = null;
/**
 * Get the shared circuit breaker instance
 * This ensures all jobs use the same circuit breaker
 */
function getSharedCircuitBreaker() {
    if (!circuitBreakerInstance) {
        // Initialize the circuit breaker with configuration from config.ts
        circuitBreakerInstance = new improvedImprovedCircuitBreaker_1.ImprovedImprovedCircuitBreaker({
            memoryThresholdPercent: config_1.config.circuitBreaker.memoryThresholdPercent,
            cpuThresholdPercent: config_1.config.circuitBreaker.cpuThresholdPercent,
            errorThresholdCount: config_1.config.circuitBreaker.errorThresholdCount,
            resetTimeoutMs: config_1.config.circuitBreaker.resetTimeoutMs,
            checkIntervalMs: config_1.config.circuitBreaker.checkIntervalMs,
            consecutiveReadingsForOpen: config_1.config.circuitBreaker.consecutiveReadingsForOpen,
            consecutiveReadingsForClose: config_1.config.circuitBreaker.consecutiveReadingsForClose,
            consecutiveReadingsForDegraded: config_1.config.circuitBreaker.consecutiveReadingsForDegraded,
            degradedMemoryThresholdPercent: config_1.config.circuitBreaker.degradedMemoryThresholdPercent,
            degradedCpuThresholdPercent: config_1.config.circuitBreaker.degradedCpuThresholdPercent,
            onStateChange: (oldState, newState) => {
                // Log state changes
                if (newState === improvedImprovedCircuitBreaker_1.CircuitState.OPEN) {
                    logger_1.logger.warn(`🔴 Circuit breaker opened - pausing job processing due to system constraints`);
                    // Send email notification about circuit breaker opening
                    (0, emailService_1.sendEmailNotification)(emailService_1.EmailNotificationType.SYSTEM_OVERLOAD, {
                        timestamp: new Date().toISOString(),
                        message: "Circuit breaker opened due to system resource constraints",
                        oldState,
                        newState,
                        memoryUsage: `${(((os_1.default.totalmem() - os_1.default.freemem()) / os_1.default.totalmem()) * 100).toFixed(2)}%`,
                        cpuUsage: `${((os_1.default.loadavg()[0] / os_1.default.cpus().length) * 100).toFixed(2)}%`,
                    }).catch((err) => logger_1.logger.error(`Failed to send circuit breaker email: ${err}`));
                }
                else if (newState === improvedImprovedCircuitBreaker_1.CircuitState.CLOSED) {
                    // When circuit breaker closes (resets), log the event
                    logger_1.logger.info(`🟢 Circuit breaker closed - resuming normal job processing`);
                    // This is where we would previously run the autoResetCircuitBreaker job
                    // Now we handle it directly here, which is more efficient
                    logger_1.logger.info(`🔄 Circuit breaker has auto-reset itself - jobs will resume on their next scheduled run`);
                    // No need to restart jobs - they will run on their next scheduled time
                    // This is more reliable than forcing all jobs to restart at once
                }
                else if (newState === improvedImprovedCircuitBreaker_1.CircuitState.HALF_OPEN) {
                    logger_1.logger.info(`🟡 Circuit breaker half-open - testing system recovery`);
                }
                else if (newState === improvedImprovedCircuitBreaker_1.CircuitState.DEGRADED) {
                    logger_1.logger.info(`🟠 Circuit breaker in degraded mode - running with reduced capacity`);
                }
            },
        });
        logger_1.logger.info("🔄 Shared circuit breaker initialized");
    }
    return circuitBreakerInstance;
}
/**
 * Reset the shared circuit breaker instance
 * This is useful for testing or when you want to force a reset
 */
function resetSharedCircuitBreaker() {
    if (circuitBreakerInstance) {
        circuitBreakerInstance.resetErrors();
        logger_1.logger.info("🔄 Shared circuit breaker reset");
    }
}
/**
 * Execute a function with circuit breaker protection
 * @param jobName Name of the job (for logging)
 * @param fn Function to execute
 * @returns Result of the function or null if circuit breaker is open
 */
async function withCircuitBreaker(jobName, fn) {
    const circuitBreaker = getSharedCircuitBreaker();
    // Check if circuit breaker allows execution
    if (!(await circuitBreaker.isClosed())) {
        logger_1.logger.warn(`⚠️ Circuit breaker is ${circuitBreaker.getState()}, skipping job ${jobName}`);
        return null;
    }
    try {
        logger_1.logger.info(`🚀 Executing job ${jobName} with circuit breaker protection`);
        return await fn();
    }
    catch (error) {
        // Record error in circuit breaker
        circuitBreaker.recordError();
        logger_1.logger.error(`❌ Error in job ${jobName}:`, error);
        throw error;
    }
}
