"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EmailTemplate = void 0;
exports.renderEmailTemplate = renderEmailTemplate;
// scraper/email/renderer.ts
const path_1 = __importDefault(require("path"));
const logger_js_1 = require("../utils/logger.js");
// Path to email templates
const TEMPLATES_DIR = path_1.default.join(process.cwd(), "email/templates");
/**
 * Email template types
 */
var EmailTemplate;
(function (EmailTemplate) {
    EmailTemplate["SYSTEM_ALERT"] = "system-alert";
    EmailTemplate["ERROR_REPORT"] = "error-report";
    EmailTemplate["USAGE_REPORT"] = "usage-report";
    EmailTemplate["JOB_SUMMARY"] = "job-summary";
})(EmailTemplate || (exports.EmailTemplate = EmailTemplate = {}));
/**
 * Render an email template with the provided data
 *
 * @param template The email template to render
 * @param data Data to populate the template
 * @returns Promise with the rendered email (subject, html, text)
 */
async function renderEmailTemplate(template, data = {}) {
    try {
        logger_js_1.logger.info(`🔄 Rendering email template: ${template}`);
        // Log template data for debugging
        if (template === EmailTemplate.JOB_SUMMARY) {
            logger_js_1.logger.info(`📊 Job Summary Template Data:`, {
                jobsProcessed: data.jobsProcessed,
                jobsSucceeded: data.jobsSucceeded,
                jobsFailed: data.jobsFailed,
                processingTime: data.processingTime,
                reportDate: data.reportDate,
            });
        }
        // Add common data
        const templateData = {
            ...data,
            appName: process.env.APP_NAME || "Hirli",
            appUrl: process.env.PUBLIC_BASE_URL || "https://hirli.co",
            supportEmail: process.env.SUPPORT_EMAIL || "<EMAIL>",
            year: new Date().getFullYear(),
        };
        // Generate simple HTML templates for each type
        let subject;
        let html;
        let text;
        switch (template) {
            case EmailTemplate.SYSTEM_ALERT:
                subject = `🔔 ${templateData.appName} System Alert`;
                html = generateSystemAlertHTML(templateData);
                text = generateSystemAlertText(templateData);
                break;
            case EmailTemplate.ERROR_REPORT:
                subject = `❌ ${templateData.appName} Error Report`;
                html = generateErrorReportHTML(templateData);
                text = generateErrorReportText(templateData);
                break;
            case EmailTemplate.USAGE_REPORT:
                subject = `📊 ${templateData.appName} Usage Report`;
                html = generateUsageReportHTML(templateData);
                text = generateUsageReportText(templateData);
                break;
            case EmailTemplate.JOB_SUMMARY:
                subject = `📋 ${templateData.appName} Job Summary`;
                html = generateJobSummaryHTML(templateData);
                text = generateJobSummaryText(templateData);
                break;
            default:
                subject = `Notification from ${templateData.appName}`;
                html = `<p>${templateData.message || "You have a new notification."}</p>`;
                text = templateData.message || "You have a new notification.";
                break;
        }
        logger_js_1.logger.info(`✅ Successfully rendered template: ${template}`);
        return { subject, html, text };
    }
    catch (error) {
        logger_js_1.logger.error(`❌ Error rendering email template ${template}:`, error);
        return null;
    }
}
/**
 * Generate HTML for system alert template
 */
function generateSystemAlertHTML(data) {
    return `
    <html>
      <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
        <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
          <h1 style="color: #e74c3c;">🔔 System Alert</h1>
          <p><strong>Alert Type:</strong> ${data.alertType || "System Alert"}</p>
          <p><strong>Time:</strong> ${data.timestamp || new Date().toISOString()}</p>
          <p><strong>Message:</strong> ${data.message || "A system alert has been triggered."}</p>
          ${data.details ? `<p><strong>Details:</strong> ${data.details}</p>` : ""}
          <hr style="margin: 20px 0;">
          <p style="font-size: 12px; color: #666;">
            This is an automated message from ${data.appName}.
          </p>
        </div>
      </body>
    </html>
  `;
}
/**
 * Generate text for system alert template
 */
function generateSystemAlertText(data) {
    return `
System Alert

Alert Type: ${data.alertType || "System Alert"}
Time: ${data.timestamp || new Date().toISOString()}
Message: ${data.message || "A system alert has been triggered."}
${data.details ? `Details: ${data.details}` : ""}

This is an automated message from ${data.appName}.
  `.trim();
}
/**
 * Generate HTML for error report template
 */
function generateErrorReportHTML(data) {
    return `
    <html>
      <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
        <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
          <h1 style="color: #e74c3c;">❌ Error Report</h1>
          <p><strong>Error:</strong> ${data.error || "An error occurred"}</p>
          <p><strong>Time:</strong> ${data.timestamp || new Date().toISOString()}</p>
          ${data.stack ? `<pre style="background: #f4f4f4; padding: 10px; overflow-x: auto;">${data.stack}</pre>` : ""}
          ${data.context ? `<p><strong>Context:</strong> ${data.context}</p>` : ""}
          <hr style="margin: 20px 0;">
          <p style="font-size: 12px; color: #666;">
            This is an automated error report from ${data.appName}.
          </p>
        </div>
      </body>
    </html>
  `;
}
/**
 * Generate text for error report template
 */
function generateErrorReportText(data) {
    return `
Error Report

Error: ${data.error || "An error occurred"}
Time: ${data.timestamp || new Date().toISOString()}
${data.stack ? `Stack Trace: ${data.stack}` : ""}
${data.context ? `Context: ${data.context}` : ""}

This is an automated error report from ${data.appName}.
  `.trim();
}
/**
 * Generate HTML for usage report template
 */
function generateUsageReportHTML(data) {
    return `
    <html>
      <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
        <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
          <h1 style="color: #3498db;">📊 Usage Report</h1>
          <p><strong>Report Period:</strong> ${data.reportPeriod || "N/A"}</p>
          <p><strong>Total Users:</strong> ${data.totalUsers || 0}</p>
          <p><strong>Active Users:</strong> ${data.activeUsers || 0}</p>
          <p><strong>Total Requests:</strong> ${data.totalRequests || 0}</p>
          ${data.additionalMetrics ? `<p><strong>Additional Metrics:</strong> ${JSON.stringify(data.additionalMetrics)}</p>` : ""}
          <hr style="margin: 20px 0;">
          <p style="font-size: 12px; color: #666;">
            This is an automated usage report from ${data.appName}.
          </p>
        </div>
      </body>
    </html>
  `;
}
/**
 * Generate text for usage report template
 */
function generateUsageReportText(data) {
    return `
Usage Report

Report Period: ${data.reportPeriod || "N/A"}
Total Users: ${data.totalUsers || 0}
Active Users: ${data.activeUsers || 0}
Total Requests: ${data.totalRequests || 0}
${data.additionalMetrics ? `Additional Metrics: ${JSON.stringify(data.additionalMetrics)}` : ""}

This is an automated usage report from ${data.appName}.
  `.trim();
}
/**
 * Generate HTML for job summary template
 */
function generateJobSummaryHTML(data) {
    return `
    <html>
      <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
        <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
          <h1 style="color: #27ae60;">📋 Job Summary</h1>
          <p><strong>Report Date:</strong> ${data.reportDate || new Date().toLocaleDateString()}</p>
          <p><strong>Jobs Processed:</strong> ${data.jobsProcessed || 0}</p>
          <p><strong>Jobs Succeeded:</strong> ${data.jobsSucceeded || 0}</p>
          <p><strong>Jobs Failed:</strong> ${data.jobsFailed || 0}</p>
          <p><strong>Processing Time:</strong> ${data.processingTime || "N/A"}</p>
          ${data.details ? `<p><strong>Details:</strong> ${data.details}</p>` : ""}
          <hr style="margin: 20px 0;">
          <p style="font-size: 12px; color: #666;">
            This is an automated job summary from ${data.appName}.
          </p>
        </div>
      </body>
    </html>
  `;
}
/**
 * Generate text for job summary template
 */
function generateJobSummaryText(data) {
    return `
Job Summary

Report Date: ${data.reportDate || new Date().toLocaleDateString()}
Jobs Processed: ${data.jobsProcessed || 0}
Jobs Succeeded: ${data.jobsSucceeded || 0}
Jobs Failed: ${data.jobsFailed || 0}
Processing Time: ${data.processingTime || "N/A"}
${data.details ? `Details: ${data.details}` : ""}

This is an automated job summary from ${data.appName}.
  `.trim();
}
