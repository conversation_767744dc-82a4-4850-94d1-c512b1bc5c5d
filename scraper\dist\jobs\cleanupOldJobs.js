"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.cleanupOldJobs = cleanupOldJobs;
const client_1 = require("@prisma/client");
const logger_js_1 = require("../utils/logger.js");
const prisma = new client_1.PrismaClient();
async function cleanupOldJobs() {
    const startTime = Date.now();
    let success = false;
    let failureReason = "";
    let count = 0;
    try {
        const cutoff = new Date(Date.now() - 1000 * 60 * 60 * 24); // 24 hours ago
        const result = await prisma.jobListing.updateMany({
            where: {
                lastCheckedAt: { lt: cutoff },
                isActive: true,
            },
            data: {
                isActive: false,
                closedAt: new Date(),
            },
        });
        count = result.count;
        logger_js_1.logger.info(`🧹 Marked ${result.count} jobs as inactive`);
        success = true;
    }
    catch (error) {
        failureReason = error instanceof Error ? error.message : String(error);
        logger_js_1.logger.error(`❌ Error cleaning up old jobs:`, error);
    }
    finally {
        const duration = Date.now() - startTime;
        // Track this job in the global stats
        logger_js_1.logger.jobStats({
            jobType: "cleanupOldJobs",
            duration,
            processed: count,
            succeeded: success ? count : 0,
            failed: success ? 0 : count,
            details: {
                error: failureReason || undefined,
            },
        });
        await prisma.$disconnect();
    }
}
