// scraper/workers/workerPool.ts
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>er<PERSON>ontex<PERSON>, Page, chromium } from "playwright";
import { Worker, WorkerPoolOptions } from "../utils/types";
import { logger } from "../utils/logger";
import { createStealthContext } from "../utils/playwrightStealth";
import { generateRandomFingerprint } from "../utils/browserFingerprint";

export class WorkerPool {
  private workers: Worker[] = [];
  private maxWorkers: number;
  private headless: boolean;
  private slowMo: number;
  private recycleThreshold: number;
  private isShuttingDown: boolean = false;

  constructor(options: WorkerPoolOptions) {
    this.maxWorkers = options.maxWorkers;
    this.headless = options.headless;
    this.slowMo = options.slowMo || 0;
    this.recycleThreshold = options.recycleThreshold || 10;
  }

  async initialize(): Promise<void> {
    logger.info(
      `🚀 Initializing worker pool with ${this.maxWorkers} workers...`
    );

    for (let i = 0; i < this.maxWorkers; i++) {
      try {
        const worker = await this.createWorker(i);
        this.workers.push(worker);
        logger.info(`✅ Worker ${i} initialized successfully`);
      } catch (error) {
        logger.error(`❌ Failed to initialize worker ${i}:`, error);
        throw error;
      }
    }

    logger.info(
      `✅ Worker pool initialized with ${this.workers.length} workers`
    );
  }

  private async createWorker(id: number): Promise<Worker> {
    const browser = await chromium.launch({
      headless: this.headless,
      slowMo: this.slowMo,
      args: [
        "--no-sandbox",
        "--disable-setuid-sandbox",
        "--disable-dev-shm-usage",
        "--disable-accelerated-2d-canvas",
        "--no-first-run",
        "--no-zygote",
        "--disable-gpu",
        "--disable-background-timer-throttling",
        "--disable-backgrounding-occluded-windows",
        "--disable-renderer-backgrounding",
      ],
    });

    const fingerprint = generateRandomFingerprint();
    const context = await createStealthContext(browser, {
      viewport: fingerprint.viewport || { width: 1920, height: 1080 },
      userAgent: fingerprint.userAgent,
      locale: fingerprint.locale,
      timezoneId: fingerprint.timezoneId,
      deviceScaleFactor: fingerprint.deviceScaleFactor,
    });
    const page = await context.newPage();

    return {
      id,
      browser,
      context,
      page,
      busy: false,
      lastUsed: new Date(),
      captchaCount: 0,
      jobsProcessed: 0,
      errorCount: 0,
    };
  }

  async getAvailableWorker(): Promise<Worker | null> {
    if (this.isShuttingDown) {
      return null;
    }

    const availableWorker = this.workers.find(
      (worker) =>
        !worker.busy && !worker.markedForShutdown && !worker.markedForRecycling
    );

    if (availableWorker) {
      availableWorker.busy = true;
      availableWorker.lastUsed = new Date();
      return availableWorker;
    }

    return null;
  }

  async getSpecificWorker(workerId: number): Promise<Worker | null> {
    const worker = this.workers.find((w) => w.id === workerId);
    if (worker && !worker.busy && !worker.markedForShutdown) {
      worker.busy = true;
      worker.lastUsed = new Date();
      return worker;
    }
    return null;
  }

  releaseWorker(worker: Worker): void {
    worker.busy = false;
    worker.jobsProcessed++;

    // Check if worker needs recycling
    if (worker.jobsProcessed >= this.recycleThreshold) {
      worker.markedForRecycling = true;
      this.recycleWorker(worker).catch((error) => {
        logger.error(`❌ Error recycling worker ${worker.id}:`, error);
      });
    }
  }

  private async recycleWorker(worker: Worker): Promise<void> {
    try {
      logger.info(`🔄 Recycling worker ${worker.id}...`);

      await worker.browser.close();

      const newWorker = await this.createWorker(worker.id);
      const index = this.workers.findIndex((w) => w.id === worker.id);
      if (index !== -1) {
        this.workers[index] = newWorker;
      }

      logger.info(`✅ Worker ${worker.id} recycled successfully`);
    } catch (error) {
      logger.error(`❌ Error recycling worker ${worker.id}:`, error);
    }
  }

  getTotalWorkerCount(): number {
    return this.workers.length;
  }

  getCurrentMaxWorkers(): number {
    return this.maxWorkers;
  }

  setMaxWorkers(newMax: number): void {
    this.maxWorkers = newMax;
  }

  async rotateProxyForWorker(worker: Worker): Promise<boolean> {
    // Placeholder for proxy rotation logic
    return true;
  }

  async shutdown(): Promise<void> {
    this.isShuttingDown = true;
    logger.info(`🛑 Shutting down worker pool...`);

    const shutdownPromises = this.workers.map(async (worker) => {
      try {
        await worker.browser.close();
        logger.info(`✅ Worker ${worker.id} shut down successfully`);
      } catch (error) {
        logger.error(`❌ Error shutting down worker ${worker.id}:`, error);
      }
    });

    await Promise.all(shutdownPromises);
    this.workers = [];
    logger.info(`✅ Worker pool shut down complete`);
  }
}
