"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.handleCaptchaIfPresent = handleCaptchaIfPresent;
const logger_js_1 = require("../utils/logger.js");
/**
 * Enhanced CAPTCHA detection with more comprehensive checks
 * and detailed logging for better debugging
 */
async function handleCaptchaIfPresent(page) {
    try {
        // Check URL for CAPTCHA indicators
        const url = page.url();
        const hasCaptchaInUrl = url.includes("/sorry") ||
            url.includes("captcha") ||
            url.includes("challenge") ||
            url.includes("interstitial") ||
            url.includes("consent.google");
        if (hasCaptchaInUrl) {
            logger_js_1.logger.warn(`🧩 CAPTCHA detected in URL: ${url}`);
            return {
                captchaDetected: true,
                captchaSolved: false,
                reason: `URL contains CAPTCHA indicators: ${url}`,
            };
        }
        // Check page title for CAPTCHA indicators
        const title = await page.title().catch(() => "");
        const hasCaptchaInTitle = title.includes("Security Check") ||
            title.includes("Attention Required") ||
            title.includes("CAPTCHA") ||
            title.includes("Unusual traffic") ||
            title.includes("Robot Check") ||
            title.includes("Human verification");
        if (hasCaptchaInTitle) {
            logger_js_1.logger.warn(`🧩 CAPTCHA detected in page title: ${title}`);
            return {
                captchaDetected: true,
                captchaSolved: false,
                reason: `Page title indicates CAPTCHA: ${title}`,
            };
        }
        // Check for CAPTCHA elements in the page
        const hasCaptchaElements = await page
            .evaluate(() => {
            // Check for reCAPTCHA elements
            const recaptchaElements = document.querySelectorAll('iframe[src*="recaptcha"], iframe[src*="captcha"], div.g-recaptcha, div[data-sitekey], .captcha-container, #captcha');
            // Check for text content indicating CAPTCHA
            const bodyText = document.body.innerText.toLowerCase();
            const captchaTextIndicators = [
                "captcha",
                "unusual traffic",
                "verify you're a human",
                "security check",
                "automated queries",
                "suspicious activity",
                "verify you are not a robot",
                "please confirm you are a human",
                "we need to make sure you're not a robot",
                "we detected unusual activity",
                "please solve this puzzle",
                "complete the security check",
                "we need to verify that you are not a robot",
            ];
            const hasTextIndicators = captchaTextIndicators.some((indicator) => bodyText.includes(indicator));
            return recaptchaElements.length > 0 || hasTextIndicators;
        })
            .catch(() => false);
        if (hasCaptchaElements) {
            logger_js_1.logger.warn(`🧩 CAPTCHA elements detected in page`);
            return {
                captchaDetected: true,
                captchaSolved: false,
                reason: "CAPTCHA elements found in page",
            };
        }
        // Check for blocked access or error pages
        const isBlockedPage = await page
            .evaluate(() => {
            const bodyText = document.body.innerText.toLowerCase();
            const blockIndicators = [
                "access denied",
                "blocked",
                "your ip address has been blocked",
                "your access to this site has been limited",
                "too many requests",
                "rate limited",
                "temporarily blocked",
                "we've detected unusual activity",
            ];
            return blockIndicators.some((indicator) => bodyText.includes(indicator));
        })
            .catch(() => false);
        if (isBlockedPage) {
            logger_js_1.logger.warn(`🚫 Access blocked or rate limited`);
            return {
                captchaDetected: true,
                captchaSolved: false,
                reason: "Access blocked or rate limited",
            };
        }
        // If we reach here, no CAPTCHA was detected
        return { captchaDetected: false, captchaSolved: false };
    }
    catch (error) {
        // If there's an error during detection, log it and assume there might be a CAPTCHA
        logger_js_1.logger.error(`❌ Error during CAPTCHA detection: ${error}`);
        return {
            captchaDetected: true,
            captchaSolved: false,
            reason: `Error during detection: ${error instanceof Error ? error.message : String(error)}`,
        };
    }
}
