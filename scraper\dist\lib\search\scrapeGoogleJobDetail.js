"use strict";
// cron/lib/search/scrapeGoogleJobDetail.ts
Object.defineProperty(exports, "__esModule", { value: true });
exports.scrapeGoogleJobDetail = scrapeGoogleJobDetail;
const logger_1 = require("../../utils/logger");
/**
 * Scrape detailed job information from a Google Jobs detail page
 * @param page Playwright page object
 * @param url URL of the Google Jobs detail page
 * @returns Detailed job information
 */
async function scrapeGoogleJobDetail(page, url) {
    try {
        logger_1.logger.info(`🔍 Scraping Google job detail from: ${url}`);
        await page.goto(url, { waitUntil: "domcontentloaded", timeout: 30000 });
        await page.waitForTimeout(2000);
        // Get the HTML content but don't save to disk
        const html = await page.content();
        logger_1.logger.info(`📄 HTML content retrieved (${html.length} bytes)`);
        // Disabled file storage to save disk space
        logger_1.logger.info(`🚫 File storage (HTML and screenshots) disabled to save disk space and memory`);
        // Extract job details
        const jobDetails = await page.evaluate(() => {
            const details = {
                title: "",
                company: "",
                location: "",
                description: "",
            };
            try {
                // Extract job title
                const titleElement = document.querySelector("h2.tNxQIb");
                if (titleElement) {
                    details.title = titleElement.textContent?.trim() || "";
                }
                // Extract company name
                const companyElement = document.querySelector(".wHYlTd.MKCbgd.a3jPc");
                if (companyElement) {
                    details.company = companyElement.textContent?.trim() || "";
                }
                // Extract location
                const locationElement = document.querySelector(".wHYlTd.FqK3wc.MKCbgd");
                if (locationElement) {
                    details.location = locationElement.textContent?.trim() || "";
                }
                // Extract job description
                const descriptionElement = document.querySelector(".HBvzbc");
                if (descriptionElement) {
                    details.description = descriptionElement.textContent?.trim() || "";
                }
                // Extract employment type
                const jobTypeElements = document.querySelectorAll(".I2Cbhb");
                jobTypeElements.forEach((element) => {
                    const text = element.textContent?.trim() || "";
                    if (text.includes("Full-time") ||
                        text.includes("Part-time") ||
                        text.includes("Contract") ||
                        text.includes("Temporary") ||
                        text.includes("Internship")) {
                        details.employmentType = text;
                    }
                });
                // Extract salary information
                const salaryElement = document.querySelector(".YgLbBe");
                if (salaryElement) {
                    details.salary = salaryElement.textContent?.trim() || "";
                }
                // Extract benefits
                const benefits = [];
                const benefitElements = document.querySelectorAll(".EgQyN");
                benefitElements.forEach((element) => {
                    const text = element.textContent?.trim() || "";
                    if (text) {
                        benefits.push(text);
                    }
                });
                if (benefits.length > 0) {
                    details.benefits = benefits;
                }
                // Extract posted date
                const dateElement = document.querySelector(".KKh3md");
                if (dateElement) {
                    details.postedDate = dateElement.textContent?.trim() || "";
                }
                // Extract application URL
                const applyButton = document.querySelector("a.pMhGee.Co68jc.j0vryd");
                if (applyButton && applyButton.hasAttribute("href")) {
                    details.applicationUrl = applyButton.getAttribute("href") || "";
                }
                // Extract requirements
                const requirementsList = [];
                const requirementsSection = Array.from(document.querySelectorAll(".Ld2paf")).find((el) => el.textContent?.includes("Qualifications") ||
                    el.textContent?.includes("Requirements"));
                if (requirementsSection) {
                    const listItems = requirementsSection.querySelectorAll("li");
                    listItems.forEach((item) => {
                        const text = item.textContent?.trim() || "";
                        if (text) {
                            requirementsList.push(text);
                        }
                    });
                    if (requirementsList.length > 0) {
                        details.requirements = requirementsList;
                    }
                }
            }
            catch (error) {
                console.error("Error extracting job details:", error);
            }
            return details;
        });
        logger_1.logger.info(`✅ Successfully scraped job details for: ${jobDetails.title}`);
        logger_1.logger.info(`  Company: ${jobDetails.company}`);
        logger_1.logger.info(`  Location: ${jobDetails.location}`);
        logger_1.logger.info(`  Employment Type: ${jobDetails.employmentType || "Not specified"}`);
        logger_1.logger.info(`  Salary: ${jobDetails.salary || "Not specified"}`);
        logger_1.logger.info(`  Posted Date: ${jobDetails.postedDate || "Not specified"}`);
        return jobDetails;
    }
    catch (error) {
        logger_1.logger.error(`❌ Error scraping Google job detail: ${error}`);
        return {
            title: "",
            company: "",
            location: "",
            description: "",
        };
    }
}
