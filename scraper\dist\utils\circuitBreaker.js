"use strict";
// scraper/utils/circuitBreaker.ts
// Circuit breaker implementation for scraper service
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CircuitBreaker = exports.CircuitState = void 0;
const logger_js_1 = require("./logger.js");
const os_1 = __importDefault(require("os"));
/**
 * Circuit breaker states
 */
var CircuitState;
(function (CircuitState) {
    CircuitState["CLOSED"] = "CLOSED";
    CircuitState["OPEN"] = "OPEN";
    CircuitState["HALF_OPEN"] = "HALF_OPEN";
    CircuitState["DEGRADED"] = "DEGRADED";
})(CircuitState || (exports.CircuitState = CircuitState = {}));
/**
 * Simple Circuit Breaker for scraper service
 */
class CircuitBreaker {
    state = CircuitState.CLOSED;
    lastStateChange = new Date();
    errorCount = 0;
    resetTimeout = null;
    checkInterval = null;
    memoryThresholdPercent;
    cpuThresholdPercent;
    errorThresholdCount;
    resetTimeoutMs;
    checkIntervalMs;
    onStateChange;
    constructor(options = {}) {
        this.memoryThresholdPercent = options.memoryThresholdPercent ?? 85;
        this.cpuThresholdPercent = options.cpuThresholdPercent ?? 85;
        this.errorThresholdCount = options.errorThresholdCount ?? 3;
        this.resetTimeoutMs = options.resetTimeoutMs ?? 60000;
        this.checkIntervalMs = options.checkIntervalMs ?? 30000;
        this.onStateChange = options.onStateChange;
        logger_js_1.logger.info(`🔄 Circuit breaker initialized - Memory: ${this.memoryThresholdPercent}%, CPU: ${this.cpuThresholdPercent}%`);
    }
    isClosed() {
        return this.state === CircuitState.CLOSED || this.state === CircuitState.DEGRADED;
    }
    getState() {
        return this.state;
    }
    recordError() {
        this.errorCount++;
        logger_js_1.logger.warn(`⚠️ Circuit breaker recorded error (${this.errorCount}/${this.errorThresholdCount})`);
        if (this.errorCount >= this.errorThresholdCount) {
            this.openCircuit("Too many errors");
        }
    }
    openCircuit(reason = "manual") {
        if (this.state !== CircuitState.OPEN) {
            const oldState = this.state;
            this.state = CircuitState.OPEN;
            this.lastStateChange = new Date();
            logger_js_1.logger.warn(`🔴 Circuit breaker opened (reason: ${reason})`);
            if (this.onStateChange) {
                this.onStateChange(oldState, this.state);
            }
            this.scheduleReset();
        }
    }
    closeCircuit() {
        if (this.state !== CircuitState.CLOSED) {
            const oldState = this.state;
            this.state = CircuitState.CLOSED;
            this.lastStateChange = new Date();
            this.errorCount = 0;
            logger_js_1.logger.info("🟢 Circuit breaker closed");
            if (this.onStateChange) {
                this.onStateChange(oldState, this.state);
            }
            if (this.resetTimeout) {
                clearTimeout(this.resetTimeout);
                this.resetTimeout = null;
            }
        }
    }
    scheduleReset() {
        if (this.resetTimeout) {
            clearTimeout(this.resetTimeout);
        }
        this.resetTimeout = setTimeout(() => {
            this.attemptReset();
        }, this.resetTimeoutMs);
        logger_js_1.logger.info(`⏳ Circuit breaker will attempt reset in ${this.resetTimeoutMs / 1000}s`);
    }
    attemptReset() {
        if (this.state === CircuitState.OPEN) {
            const { memoryUsage, cpuUsage } = this.getSystemResources();
            if (memoryUsage < this.memoryThresholdPercent && cpuUsage < this.cpuThresholdPercent) {
                const oldState = this.state;
                this.state = CircuitState.HALF_OPEN;
                this.lastStateChange = new Date();
                logger_js_1.logger.info("🟡 Circuit breaker half-open - testing recovery");
                if (this.onStateChange) {
                    this.onStateChange(oldState, this.state);
                }
            }
            else {
                logger_js_1.logger.warn(`⚠️ Resources still constrained (Memory: ${memoryUsage.toFixed(2)}%, CPU: ${cpuUsage.toFixed(2)}%)`);
                this.scheduleReset();
            }
        }
    }
    getSystemResources() {
        const totalMemory = os_1.default.totalmem();
        const freeMemory = os_1.default.freemem();
        const memoryUsage = ((totalMemory - freeMemory) / totalMemory) * 100;
        const cpuCount = os_1.default.cpus().length;
        const loadAvg = os_1.default.loadavg()[0];
        const cpuUsage = (loadAvg / cpuCount) * 100;
        return { memoryUsage, cpuUsage };
    }
    stopMonitoring() {
        if (this.checkInterval) {
            clearInterval(this.checkInterval);
            this.checkInterval = null;
        }
        if (this.resetTimeout) {
            clearTimeout(this.resetTimeout);
            this.resetTimeout = null;
        }
    }
}
exports.CircuitBreaker = CircuitBreaker;
