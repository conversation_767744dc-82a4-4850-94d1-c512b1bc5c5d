"use strict";
// cron/utils/prismaClient.ts
// Improved Prisma client management with better connection handling and logging
Object.defineProperty(exports, "__esModule", { value: true });
exports.getPrismaClient = getPrismaClient;
exports.executeRawQuery = executeRawQuery;
exports.getCombinedClient = getCombinedClient;
exports.disconnectPrisma = disconnectPrisma;
const client_1 = require("@prisma/client");
const logger_1 = require("./logger");
const child_process_1 = require("child_process");
const util_1 = require("util");
const execAsync = (0, util_1.promisify)(child_process_1.exec);
// Cache for Prisma clients
const clients = {};
/**
 * Get a Prisma client for a specific schema
 * @param schema The schema to use ('web' or 'worker')
 * @returns A Prisma client for the specified schema
 */
async function getPrismaClient(schema = "web") {
    // If client already exists in cache, return it
    if (clients[schema]) {
        return clients[schema];
    }
    try {
        // Create a new Prisma client for the specified schema with improved logging and connection pool settings
        const client = new client_1.PrismaClient({
            datasources: {
                db: {
                    url: process.env.DATABASE_URL,
                },
            },
            log: [
                { level: "warn", emit: "event" },
                { level: "error", emit: "event" },
                { level: "info", emit: "event" },
            ],
            // Add connection pool configuration to prevent exhaustion
            __internal: {
                engine: {
                    connectionLimit: 3, // Reduced from default 9 to 3 for cron jobs
                },
            },
        });
        // Add event listeners for logging
        client.$on("warn", (e) => {
            logger_1.logger.warn(`Prisma warning (${schema}): ${e.message}`);
        });
        client.$on("error", (e) => {
            logger_1.logger.error(`Prisma error (${schema}): ${e.message}`);
        });
        client.$on("info", (e) => {
            logger_1.logger.info(`Prisma info (${schema}): ${e.message}`);
        });
        // Store in cache
        clients[schema] = client;
        logger_1.logger.info(`✅ Prisma client for ${schema} schema initialized successfully`);
        return client;
    }
    catch (error) {
        logger_1.logger.error(`❌ Failed to initialize Prisma client for ${schema} schema:`, error);
        throw new Error(`Failed to initialize Prisma client for ${schema} schema`);
    }
}
/**
 * Execute a raw query on the database
 * @param query The SQL query to execute
 * @param params Parameters for the query
 * @param schema The schema to use for the query
 * @returns The query result
 */
async function executeRawQuery(query, params = [], schema = "web") {
    const client = await getPrismaClient();
    try {
        // First set the search path
        await client.$executeRawUnsafe(`SET search_path TO "${schema}"`);
        // Then execute the actual query
        return await client.$queryRawUnsafe(query, ...params);
    }
    catch (error) {
        logger_1.logger.error(`❌ Error executing raw query in schema ${schema}:`, error);
        throw error;
    }
}
/**
 * Get a combined client that can access models from multiple schemas
 * This is a workaround for accessing models from different schemas
 * @returns An object with clients for each schema
 */
async function getCombinedClient() {
    const webClient = await getPrismaClient("web");
    const workerClient = await getPrismaClient("worker");
    // Use raw queries for web schema models
    return {
        web: webClient,
        worker: workerClient,
        // Additional web schema models with custom queries
        webCustom: {
            Profile: {
                findMany: async (args) => {
                    // Convert Prisma query to SQL
                    const whereClause = args.where
                        ? `WHERE documents.resume.isParsed = true AND documents.resume.rawText IS NOT NULL`
                        : "";
                    const query = `
            SELECT p.*, d.*, r.*
            FROM "Profile" p
            JOIN "Document" d ON p.id = d."profileId"
            JOIN "Resume" r ON d.id = r."documentId"
            ${whereClause}
            LIMIT ${args.take ?? 100}
          `;
                    return executeRawQuery(query);
                },
            },
        },
    };
}
/**
 * Disconnect all Prisma clients
 * Call this when shutting down the application to properly close connections
 */
async function disconnectPrisma() {
    for (const schema in clients) {
        try {
            await clients[schema].$disconnect();
            logger_1.logger.info(`✅ Prisma client for ${schema} schema disconnected successfully`);
        }
        catch (error) {
            logger_1.logger.error(`❌ Error disconnecting Prisma client for ${schema} schema:`, error);
        }
    }
    // Clear the clients cache
    Object.keys(clients).forEach((key) => {
        delete clients[key];
    });
}
// Handle application shutdown to properly close database connections
process.on("SIGINT", async () => {
    logger_1.logger.info("Received SIGINT signal, disconnecting Prisma clients");
    await disconnectPrisma();
    process.exit(0);
});
process.on("SIGTERM", async () => {
    logger_1.logger.info("Received SIGTERM signal, disconnecting Prisma clients");
    await disconnectPrisma();
    process.exit(0);
});
// Export default client for backward compatibility
exports.default = getPrismaClient;
