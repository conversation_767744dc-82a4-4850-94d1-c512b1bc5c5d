"use strict";
// scraper/index.ts
// Main entry point for the scraper service
Object.defineProperty(exports, "__esModule", { value: true });
const logger_js_1 = require("./utils/logger.js");
const executeCommand_js_1 = require("./utils/executeCommand.js");
class ScraperService {
    isRunning = false;
    currentJob = null;
    constructor() {
        // Handle graceful shutdown
        process.on("SIGINT", () => this.shutdown());
        process.on("SIGTERM", () => this.shutdown());
    }
    async start() {
        logger_js_1.logger.info("🚀 Starting scraper service...");
        this.isRunning = true;
        // Run the scraping cycle continuously
        while (this.isRunning) {
            try {
                // Run parallel job scraper
                await this.runJob("parallel-scraper", ["npm", "run", "parallel"]);
                if (!this.isRunning)
                    break;
                // Wait between cycles
                logger_js_1.logger.info("⏳ Waiting 2 hours before next scraping cycle...");
                await this.delay(7200000); // 2 hours
                // Run job details scraper
                await this.runJob("details-scraper", ["npm", "run", "details"]);
                if (!this.isRunning)
                    break;
                // Wait between cycles
                await this.delay(1800000); // 30 minutes
                // Run job enricher
                await this.runJob("job-enricher", ["npm", "run", "enrich"]);
                if (!this.isRunning)
                    break;
                // Wait before next full cycle
                logger_js_1.logger.info("⏳ Waiting 4 hours before next full cycle...");
                await this.delay(14400000); // 4 hours
            }
            catch (error) {
                logger_js_1.logger.error("❌ Error in scraping cycle:", error);
                // Wait before retrying
                await this.delay(600000); // 10 minutes
            }
        }
    }
    async runJob(jobName, command) {
        this.currentJob = jobName;
        logger_js_1.logger.info(`🔄 Starting ${jobName}...`);
        try {
            await (0, executeCommand_js_1.executeCommandStreaming)(process.cwd(), command[0], command.slice(1), jobName);
            logger_js_1.logger.info(`✅ ${jobName} completed successfully`);
        }
        catch (error) {
            logger_js_1.logger.error(`❌ ${jobName} failed:`, error);
            throw error;
        }
        finally {
            this.currentJob = null;
        }
    }
    delay(ms) {
        return new Promise((resolve) => setTimeout(resolve, ms));
    }
    async shutdown() {
        logger_js_1.logger.info("🛑 Shutting down scraper service...");
        this.isRunning = false;
        if (this.currentJob) {
            logger_js_1.logger.info(`⏳ Waiting for current job (${this.currentJob}) to complete...`);
            // Give current job up to 5 minutes to complete
            let waitTime = 0;
            while (this.currentJob && waitTime < 300000) {
                await this.delay(5000);
                waitTime += 5000;
            }
        }
        logger_js_1.logger.info("✅ Scraper service shut down gracefully");
        process.exit(0);
    }
}
// Start the service
const scraperService = new ScraperService();
scraperService.start().catch((error) => {
    logger_js_1.logger.error("❌ Failed to start scraper service:", error);
    process.exit(1);
});
