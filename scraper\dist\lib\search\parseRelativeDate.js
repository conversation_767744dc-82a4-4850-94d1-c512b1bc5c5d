"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.parseRelativeDate = parseRelativeDate;
const logger_1 = require("../../utils/logger");
/**
 * Parse a relative date string like "2 days ago", "3 hours ago", etc. into a Date object
 * @param relativeDate The relative date string to parse
 * @returns A Date object representing the absolute date, or undefined if parsing fails
 */
function parseRelativeDate(relativeDate) {
    if (!relativeDate) {
        return undefined;
    }
    logger_1.logger.info(`🕒 Parsing relative date: "${relativeDate}"`);
    // Clean up the input
    const cleanedInput = relativeDate.trim().toLowerCase();
    // Handle "just now", "today", etc.
    if (cleanedInput === 'just now' || cleanedInput === 'now' || cleanedInput === 'today') {
        logger_1.logger.info(`🕒 Interpreted as current time`);
        return new Date();
    }
    if (cleanedInput === 'yesterday') {
        const date = new Date();
        date.setDate(date.getDate() - 1);
        logger_1.logger.info(`🕒 Interpreted as yesterday: ${date.toISOString()}`);
        return date;
    }
    // Regular expressions to match common patterns
    const minutesAgoRegex = /(\d+)\s*minute[s]?\s*ago/i;
    const hoursAgoRegex = /(\d+)\s*hour[s]?\s*ago/i;
    const daysAgoRegex = /(\d+)\s*day[s]?\s*ago/i;
    const weeksAgoRegex = /(\d+)\s*week[s]?\s*ago/i;
    const monthsAgoRegex = /(\d+)\s*month[s]?\s*ago/i;
    // Try to match each pattern
    let match;
    let date = new Date();
    if ((match = cleanedInput.match(minutesAgoRegex))) {
        const minutes = parseInt(match[1], 10);
        date.setMinutes(date.getMinutes() - minutes);
        logger_1.logger.info(`🕒 Interpreted as ${minutes} minutes ago: ${date.toISOString()}`);
        return date;
    }
    if ((match = cleanedInput.match(hoursAgoRegex))) {
        const hours = parseInt(match[1], 10);
        date.setHours(date.getHours() - hours);
        logger_1.logger.info(`🕒 Interpreted as ${hours} hours ago: ${date.toISOString()}`);
        return date;
    }
    if ((match = cleanedInput.match(daysAgoRegex))) {
        const days = parseInt(match[1], 10);
        date.setDate(date.getDate() - days);
        logger_1.logger.info(`🕒 Interpreted as ${days} days ago: ${date.toISOString()}`);
        return date;
    }
    if ((match = cleanedInput.match(weeksAgoRegex))) {
        const weeks = parseInt(match[1], 10);
        date.setDate(date.getDate() - (weeks * 7));
        logger_1.logger.info(`🕒 Interpreted as ${weeks} weeks ago: ${date.toISOString()}`);
        return date;
    }
    if ((match = cleanedInput.match(monthsAgoRegex))) {
        const months = parseInt(match[1], 10);
        date.setMonth(date.getMonth() - months);
        logger_1.logger.info(`🕒 Interpreted as ${months} months ago: ${date.toISOString()}`);
        return date;
    }
    // Try to parse absolute dates
    try {
        const absoluteDate = new Date(cleanedInput);
        if (!isNaN(absoluteDate.getTime())) {
            logger_1.logger.info(`🕒 Interpreted as absolute date: ${absoluteDate.toISOString()}`);
            return absoluteDate;
        }
    }
    catch (e) {
        // Ignore parsing errors for absolute dates
    }
    logger_1.logger.warn(`⚠️ Could not parse relative date: "${relativeDate}"`);
    return undefined;
}
