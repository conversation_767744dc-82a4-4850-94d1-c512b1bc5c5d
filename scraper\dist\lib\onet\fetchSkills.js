"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.fetchOnetSkills = fetchOnetSkills;
// cron/lib/linkedin/fetchSkills.ts
const client_1 = require("@prisma/client");
const logger_1 = require("../../utils/logger");
const promises_1 = __importDefault(require("fs/promises"));
const path_1 = __importDefault(require("path"));
const readline_1 = __importDefault(require("readline"));
const prisma = new client_1.PrismaClient();
const DATA_PATH = path_1.default.resolve("data/Technology%20Skills.txt");
// Helper – treat blank or literal "N/A" (any case) as missing
const isNA = (v) => !v || v.trim() === "" || v.trim().toLowerCase() === "n/a";
async function fetchOnetSkills() {
    logger_1.logger.info("📥 Reading O*NET technology skills file…");
    const fh = await promises_1.default.open(DATA_PATH, "r");
    const rl = readline_1.default.createInterface({
        input: fh.createReadStream(),
        crlfDelay: Infinity,
    });
    const seen = new Set(); // avoid duplicates in the same run
    let count = 0;
    let header = true;
    for await (const raw of rl) {
        if (header) {
            header = false; // skip header row
            continue;
        }
        const [socCode, // e.g. "11-1011.00"
        toolName, // e.g. "Python"
        commodityCode, // not used right now
        commodityTitle, // e.g. "Programming languages"
        isHotTech, // "Y" | "N"
        isInDemand, // "Y" | "N"
        ] = raw.split("\t");
        if (isNA(toolName))
            continue; // must have a valid tool name
        const skillName = toolName.trim();
        if (seen.has(skillName))
            continue; // duplicate within the same file
        // Skip if the skill already exists in DB
        const exists = await prisma.skills.findUnique({
            where: { name: skillName },
            select: { id: true },
        });
        if (exists)
            continue;
        // Insert new skill
        await prisma.skills.create({
            data: {
                name: skillName,
                type: commodityTitle?.trim() || null,
                source: "O*NET",
                // You can store isHotTech / isInDemand if you add columns for them
            },
        });
        seen.add(skillName);
        count++;
        logger_1.logger.debug(`🛠 Added skill: ${skillName} (${commodityTitle})`);
    }
    await fh.close();
    logger_1.logger.info(`✅ Stored ${count} new O*NET technology skills in DB`);
}
