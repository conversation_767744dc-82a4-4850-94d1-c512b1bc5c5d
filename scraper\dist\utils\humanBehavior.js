"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.simulateBrowsing = exports.simulateReading = exports.humanClick = exports.humanMouseMove = exports.humanType = exports.humanScroll = exports.humanDelay = exports.delay = void 0;
const logger_js_1 = require("./logger.js");
// Delay function with randomization to appear more human-like
const delay = (ms, randomFactor = 0.2) => {
    // Add random variation to the delay (±randomFactor%)
    const randomizedDelay = ms * (1 + (Math.random() * 2 * randomFactor - randomFactor));
    return new Promise((resolve) => setTimeout(resolve, randomizedDelay));
};
exports.delay = delay;
// Advanced human-like random delay between actions with variable timing patterns
const humanDelay = async (action) => {
    // Check if we're in production mode - use faster delays for production
    const isProduction = process.env.NODE_ENV === "production";
    // Different actions have different typical human timing patterns
    let baseDelay;
    let variability;
    if (isProduction) {
        // Much faster delays for production mode
        switch (action) {
            case "navigation":
                baseDelay = 500;
                variability = 500;
                break;
            case "scroll":
                baseDelay = 100;
                variability = 200;
                break;
            case "click":
                baseDelay = 50;
                variability = 150;
                break;
            case "typing":
                baseDelay = 50;
                variability = 100;
                break;
            case "thinking":
                baseDelay = 300;
                variability = 500;
                break;
            case "reading":
                baseDelay = 500;
                variability = 1000;
                break;
            default:
                baseDelay = 200;
                variability = 300;
        }
    }
    else {
        // Original slower delays for development mode (to avoid detection)
        switch (action) {
            case "navigation":
                baseDelay = 2000;
                variability = 2000;
                break;
            case "scroll":
                baseDelay = 500;
                variability = 1500;
                break;
            case "click":
                baseDelay = 300;
                variability = 1200;
                break;
            case "typing":
                baseDelay = 200;
                variability = 600;
                break;
            case "thinking":
                baseDelay = 3000;
                variability = 5000;
                break;
            case "reading":
                baseDelay = 5000;
                variability = 10000;
                break;
            default:
                baseDelay = 1000;
                variability = 4000;
        }
    }
    // Add some randomness to the delay
    const randomMs = baseDelay + Math.floor(Math.random() * variability);
    // Occasionally add an extra long pause (only 1% chance in production, 5% in development)
    if (Math.random() < (isProduction ? 0.01 : 0.05)) {
        const extraDelay = isProduction
            ? 500 + Math.floor(Math.random() * 1000)
            : 2000 + Math.floor(Math.random() * 3000);
        logger_js_1.logger.info(`🧠 Adding extra "thinking" time of ${Math.round(extraDelay / 1000)}s...`);
        await (0, exports.delay)(extraDelay);
    }
    await (0, exports.delay)(randomMs);
};
exports.humanDelay = humanDelay;
// Simulate human-like scrolling behavior
const humanScroll = async (page, scrollAmount = 300) => {
    // Determine number of scroll steps (1-5)
    const scrollSteps = 1 + Math.floor(Math.random() * 4);
    const scrollStep = scrollAmount / scrollSteps;
    for (let i = 0; i < scrollSteps; i++) {
        // Random scroll amount per step
        const thisStep = scrollStep + (Math.random() * 50 - 25);
        await page.evaluate((scrollY) => {
            window.scrollBy(0, scrollY);
        }, thisStep);
        // Random delay between scroll steps (100-500ms)
        await (0, exports.delay)(100 + Math.floor(Math.random() * 400));
    }
    // Pause after scrolling
    await (0, exports.humanDelay)("scroll");
};
exports.humanScroll = humanScroll;
// Simulate human-like typing with variable delays
const humanType = async (page, selector, text) => {
    await page.click(selector);
    await (0, exports.humanDelay)("click");
    // Type with human-like delays between characters
    for (const char of text) {
        await page.keyboard.type(char, { delay: 50 + Math.random() * 150 });
    }
    await (0, exports.humanDelay)("typing");
};
exports.humanType = humanType;
// Simulate human-like mouse movement
const humanMouseMove = async (page, x, y) => {
    // Move mouse in a slightly curved path
    const currentPos = await page.evaluate(() => ({
        x: window.innerWidth / 2,
        y: window.innerHeight / 2,
    }));
    const steps = 5 + Math.floor(Math.random() * 10);
    for (let i = 0; i <= steps; i++) {
        const progress = i / steps;
        const currentX = currentPos.x + (x - currentPos.x) * progress;
        const currentY = currentPos.y + (y - currentPos.y) * progress;
        // Add slight randomness to the path
        const randomX = currentX + (Math.random() - 0.5) * 10;
        const randomY = currentY + (Math.random() - 0.5) * 10;
        await page.mouse.move(randomX, randomY);
        await (0, exports.delay)(20 + Math.random() * 30);
    }
};
exports.humanMouseMove = humanMouseMove;
// Simulate human-like clicking with slight position variation
const humanClick = async (page, selector) => {
    const element = await page.locator(selector);
    const box = await element.boundingBox();
    if (box) {
        // Click at a slightly random position within the element
        const x = box.x + box.width * (0.3 + Math.random() * 0.4);
        const y = box.y + box.height * (0.3 + Math.random() * 0.4);
        await (0, exports.humanMouseMove)(page, x, y);
        await (0, exports.humanDelay)("click");
        await page.mouse.click(x, y);
    }
    else {
        // Fallback to regular click
        await element.click();
    }
    await (0, exports.humanDelay)("click");
};
exports.humanClick = humanClick;
// Simulate reading behavior by pausing and scrolling
const simulateReading = async (page, duration = 3000) => {
    const readingTime = duration + Math.random() * duration;
    const scrolls = 1 + Math.floor(Math.random() * 3);
    for (let i = 0; i < scrolls; i++) {
        await (0, exports.humanScroll)(page, 200 + Math.random() * 300);
        await (0, exports.delay)(readingTime / scrolls);
    }
    await (0, exports.humanDelay)("reading");
};
exports.simulateReading = simulateReading;
// Simulate browsing behavior with random interactions
const simulateBrowsing = async (page) => {
    // Random number of interactions (1-3)
    const interactions = 1 + Math.floor(Math.random() * 3);
    for (let i = 0; i < interactions; i++) {
        const action = Math.random();
        if (action < 0.4) {
            // Scroll
            await (0, exports.humanScroll)(page);
        }
        else if (action < 0.7) {
            // Move mouse to random position
            const x = Math.random() * 800 + 100;
            const y = Math.random() * 600 + 100;
            await (0, exports.humanMouseMove)(page, x, y);
        }
        else {
            // Pause and "read"
            await (0, exports.simulateReading)(page, 1000 + Math.random() * 2000);
        }
        await (0, exports.humanDelay)();
    }
};
exports.simulateBrowsing = simulateBrowsing;
