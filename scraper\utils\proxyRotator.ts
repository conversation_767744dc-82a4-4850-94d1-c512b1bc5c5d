// scraper/utils/proxyRotator.ts
import { logger } from "./logger.js";
import { generateRandomFingerprint } from "./browserFingerprint.js";
import { getDefaultUserAgent } from "./headers.js";
import { Worker } from "./types.js";

/**
 * Get current IP address for a worker
 */
async function getCurrentIP(worker: Worker): Promise<string | null> {
  try {
    const response = await worker.page.goto("https://api.ipify.org", {
      waitUntil: "domcontentloaded",
      timeout: 10000,
    });

    if (response?.ok()) {
      const ip = await worker.page.textContent("body");
      return ip?.trim() || null;
    }
  } catch (error) {
    logger.warn(`⚠️ Failed to get current IP for worker #${worker.id}: ${error}`);
  }
  return null;
}

/**
 * Enhanced proxy rotation with IP verification
 * This ensures we get a fresh IP address with each rotation
 */
export async function rotateProxyWithIPCheck(
  worker: Worker,
  proxyUsername: string,
  proxyPassword: string,
  proxyHost: string,
  assignedProxies: Map<number, number>,
  usedPorts: Set<number>,
  assignProxyToWorker: (workerId: number) => number,
  maxRetries: number = 3
): Promise<boolean> {
  if (!worker) return false;

  // Track if worker was busy before rotation
  const wasBusy = worker.busy;
  // Mark worker as busy during rotation to prevent concurrent use
  worker.busy = true;

  logger.info(
    `🔁 Rotating proxy for worker #${worker.id} with IP verification...`
  );

  // Set a timeout for the entire rotation process
  const rotationTimeout = setTimeout(() => {
    logger.error(`⏱️ Proxy rotation timeout for worker #${worker.id}`);
  }, 30000); // 30 second timeout

  // Store the current IP to ensure we get a different one
  let currentIP = await getCurrentIP(worker);
  logger.info(
    `🌍 Current IP for worker #${worker.id}: ${currentIP || "unknown"}`
  );

  let retryCount = 0;
  let success = false;

  while (retryCount < maxRetries && !success) {
    try {
      // 🧼 Cleanup old resources
      const currentPort = assignedProxies.get(worker.id);
      if (currentPort) usedPorts.delete(currentPort);
      assignedProxies.delete(worker.id);

      // Close old resources with timeouts
      try {
        await Promise.race([
          worker.page.close(),
          new Promise((resolve) => setTimeout(resolve, 5000)),
        ]);

        await Promise.race([
          worker.context.close(),
          new Promise((resolve) => setTimeout(resolve, 5000)),
        ]);
      } catch (err) {
        logger.warn(
          `⚠️ Failed to close old context/page for worker #${worker.id}: ${err}`
        );
      }

      // Assign a new port using the provided function
      const newPort = assignProxyToWorker(worker.id);
      const proxyUrl = `http://${proxyUsername}:${proxyPassword}@${proxyHost}:${newPort}`;

      logger.info(
        `🌍 Trying new Smartproxy for worker #${worker.id} → ${proxyUrl}`
      );

      // Generate a completely random fingerprint
      const fingerprint = generateRandomFingerprint();

      // Reuse existing browser and create a new context/page
      const context = (await Promise.race([
        worker.browser.newContext({
          httpCredentials: {
            username: proxyUsername,
            password: proxyPassword,
          },
          viewport: fingerprint.viewport,
          userAgent: getDefaultUserAgent(),
          deviceScaleFactor: fingerprint.deviceScaleFactor,
          locale: fingerprint.locale,
          timezoneId: fingerprint.timezoneId,
        }),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error("Context creation timeout")), 10000)
        ),
      ])) as any; // Type assertion to avoid TypeScript errors

      const page = (await Promise.race([
        context.newPage(),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error("Page creation timeout")), 10000)
        ),
      ])) as any;

      // Replace worker's context + page in-place
      worker.context = context;
      worker.page = page;
      worker.lastUsed = new Date();
      worker.captchaCount = 0; // Reset captcha count after rotation

      // Verify we got a new IP address
      const newIP = await getCurrentIP(worker);
      logger.info(`🌍 New IP for worker #${worker.id}: ${newIP || "unknown"}`);

      if (newIP && currentIP && newIP !== currentIP) {
        logger.info(`✅ Successfully rotated to a new IP: ${newIP}`);
        success = true;
      } else if (!currentIP || !newIP) {
        logger.info(
          `⚠️ Couldn't verify IP change, but proxy rotation completed`
        );
        success = true; // Assume success if we can't verify
      } else {
        logger.warn(`⚠️ Failed to get a new IP address, still on ${newIP}`);
        retryCount++;

        // Try again with a different port
        if (retryCount < maxRetries) {
          logger.info(
            `🔄 Retrying proxy rotation (attempt ${retryCount + 1}/${maxRetries})...`
          );

          // Close the resources we just created
          try {
            await page.close();
            await context.close();
          } catch (err) {
            logger.warn(`⚠️ Error closing resources during retry: ${err}`);
          }

          // Wait a bit before retrying
          await new Promise((resolve) => setTimeout(resolve, 2000));
          continue;
        }
      }

      // Restore previous busy state if worker wasn't busy before rotation
      if (!wasBusy) {
        worker.busy = false;
      }

      clearTimeout(rotationTimeout);

      if (success) {
        logger.info(
          `✅ Worker #${worker.id} proxy rotation completed with new IP`
        );
        return true;
      } else {
        logger.warn(
          `⚠️ Worker #${worker.id} proxy rotation completed but couldn't verify new IP after ${maxRetries} attempts`
        );
        return true; // Still return true as we did rotate the proxy
      }
    } catch (error) {
      logger.error(
        `❌ Error rotating proxy for worker #${worker.id}: ${error}`
      );
      retryCount++;

      // Wait a bit before retrying
      await new Promise((resolve) => setTimeout(resolve, 2000));
    }
  }

  // Clear the timeout since we're done
  clearTimeout(rotationTimeout);

  // If we get here, all retries failed
  logger.error(
    `❌ All proxy rotation attempts failed for worker #${worker.id}`
  );

  // If worker was not busy before, release it
  if (!wasBusy) {
    worker.busy = false;
  }

  return false;
}

/**
 * Add random delays between requests to avoid detection
 * @param min Minimum delay in milliseconds
 * @param max Maximum delay in milliseconds
 * @returns Promise that resolves after a random delay
 */
export function randomDelay(
  min: number = 3000,
  max: number = 10000
): Promise<void> {
  const delay = Math.floor(Math.random() * (max - min + 1)) + min;
  logger.info(`⏳ Adding random delay of ${delay}ms between requests`);
  return new Promise((resolve) => setTimeout(resolve, delay));
}
