"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.loadCountries = loadCountries;
exports.loadStates = loadStates;
const client_1 = require("@prisma/client");
const promises_1 = __importDefault(require("fs/promises"));
const path_1 = __importDefault(require("path"));
const logger_1 = require("../utils/logger");
const prisma = new client_1.PrismaClient();
/* ──────────────────────────────────────────────────────────────
   1️⃣  Country – we only care about the United States
   ────────────────────────────────────────────────────────────── */
async function loadCountries() {
    const us = await prisma.country.upsert({
        where: { isoCode: "US" },
        update: {}, // no changes if it already exists
        create: { name: "United States", isoCode: "US" },
    });
    logger_1.logger.debug("✅ Ensured country: United States (US)");
    return us;
}
/* ──────────────────────────────────────────────────────────────
   2️⃣  States – load from states.json and attach to the US row
   ────────────────────────────────────────────────────────────── */
async function loadStates() {
    const statesPath = path_1.default.resolve("./utils/json/states.json");
    const rows = JSON.parse(await promises_1.default.readFile(statesPath, "utf-8"));
    const us = await loadCountries();
    let inserted = 0;
    for (const { name, abbreviation } of rows) {
        if (!name || !abbreviation)
            continue; // skip incomplete rows
        /* upsert by (countryId, code) so we never duplicate */
        const dbState = await prisma.state.upsert({
            where: {
                countryId_code: {
                    countryId: us.id,
                    code: abbreviation.toUpperCase(),
                },
            },
            update: {
                name, // keep name in sync if it changed
            },
            create: {
                name,
                code: abbreviation.toUpperCase(),
                countryId: us.id,
            },
        });
        if (dbState.createdAt.getTime() === Date.now()) {
            inserted++; // count only fresh inserts
        }
        logger_1.logger.debug(`✅ State upserted: ${name} (${abbreviation})`);
    }
    logger_1.logger.info(`✅ Loaded ${inserted} new states (processed ${rows.length})`);
}
