"use strict";
// cron/scripts/downloadOnetTechSkills.ts
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.downloadOnetTechSkills = downloadOnetTechSkills;
const playwright_1 = require("playwright");
const promises_1 = __importDefault(require("fs/promises"));
const path_1 = __importDefault(require("path"));
const logger_1 = require("../../utils/logger");
const FILE_URL = "https://www.onetcenter.org/dl_files/database/db_29_2_text/Technology%20Skills.txt";
const OUTPUT_DIR = path_1.default.resolve("data");
const OUTPUT_FILE = path_1.default.join(OUTPUT_DIR, "Technology%20Skills.txt");
async function downloadOnetTechSkills() {
    logger_1.logger.info("🌐 Launching browser to download Technology Skills file...");
    // Always use headless mode for this function
    const browser = await playwright_1.chromium.launch({
        headless: true,
        args: ["--no-sandbox"], // Required for Docker environments
    });
    const context = await browser.newContext();
    const page = await context.newPage();
    try {
        logger_1.logger.info(`📥 Downloading from: ${FILE_URL}`);
        const response = await page.goto(FILE_URL);
        const content = await response?.text();
        if (!content || content.includes("<html")) {
            throw new Error("Received HTML response instead of file content. Possibly blocked.");
        }
        await promises_1.default.mkdir(OUTPUT_DIR, { recursive: true });
        await promises_1.default.writeFile(OUTPUT_FILE, content);
        logger_1.logger.info(`✅ File saved to: ${OUTPUT_FILE}`);
    }
    catch (err) {
        logger_1.logger.error("❌ Failed to download file:", err);
    }
    finally {
        await browser.close();
    }
}
await downloadOnetTechSkills();
