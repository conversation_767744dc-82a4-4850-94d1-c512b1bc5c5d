// scraper/workers/jobScraperWorker.ts
import { WorkerPool } from "./workerPool";
import { JobBatch } from "../utils/types";
import { logger } from "../utils/logger";

export interface JobBatchResult {
  batchId: string;
  success: boolean;
  jobsFound: number;
  jobsSaved: number;
  completedOccupationIds?: string[];
  error?: string;
}

export async function processJobBatch(
  workerPool: WorkerPool,
  batch: JobBatch
): Promise<JobBatchResult> {
  const worker = await workerPool.getAvailableWorker();
  
  if (!worker) {
    return {
      batchId: batch.batchId,
      success: false,
      jobsFound: 0,
      jobsSaved: 0,
      error: "No available worker",
    };
  }

  try {
    logger.info(
      `🚀 [Worker ${worker.id}] Processing batch ${batch.batchId} for ${batch.cityName}, ${batch.stateCode}`
    );

    // Store current batch info in worker
    worker.currentBatch = batch;

    let totalJobsFound = 0;
    let totalJobsSaved = 0;
    const completedOccupationIds: string[] = [];

    // Process each occupation in the batch
    for (const occupation of batch.occupations) {
      try {
        logger.info(
          `🔍 [Worker ${worker.id}] Processing occupation: ${occupation.title} in ${batch.cityName}`
        );

        // Simulate job scraping work
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Simulate finding some jobs
        const jobsFound = Math.floor(Math.random() * 10) + 1;
        const jobsSaved = Math.floor(jobsFound * 0.8); // 80% success rate

        totalJobsFound += jobsFound;
        totalJobsSaved += jobsSaved;
        completedOccupationIds.push(occupation.id);

        logger.info(
          `✅ [Worker ${worker.id}] Completed ${occupation.title}: ${jobsFound} found, ${jobsSaved} saved`
        );

      } catch (occupationError) {
        logger.error(
          `❌ [Worker ${worker.id}] Error processing occupation ${occupation.title}:`,
          occupationError
        );
        // Continue with next occupation
      }
    }

    logger.info(
      `✅ [Worker ${worker.id}] Batch ${batch.batchId} completed: ${totalJobsFound} jobs found, ${totalJobsSaved} saved`
    );

    return {
      batchId: batch.batchId,
      success: true,
      jobsFound: totalJobsFound,
      jobsSaved: totalJobsSaved,
      completedOccupationIds,
    };

  } catch (error) {
    logger.error(
      `❌ [Worker ${worker.id}] Error processing batch ${batch.batchId}:`,
      error
    );

    return {
      batchId: batch.batchId,
      success: false,
      jobsFound: 0,
      jobsSaved: 0,
      error: error instanceof Error ? error.message : String(error),
    };

  } finally {
    // Clear current batch info
    worker.currentBatch = undefined;
    
    // Release the worker back to the pool
    workerPool.releaseWorker(worker);
  }
}
